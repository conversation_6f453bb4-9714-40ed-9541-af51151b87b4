import 'dart:developer';
import 'package:flutter_onegate/services/auth_service/auth_service.dart';
import 'package:flutter_onegate/services/auth_service/enhanced_auth_storage.dart';
import 'package:flutter_onegate/services/auth_service/enhanced_token_refresh_manager.dart';
import 'package:flutter_onegate/services/auth_service/models/auth_tokens.dart';
import 'package:flutter_onegate/data/datasources/gate_storage.dart';
import 'package:get_it/get_it.dart';

/// Test utility to verify the authentication integration is working correctly
class AuthIntegrationTest {
  static Future<void> testAuthenticationFlow() async {
    try {
      log("🧪 ===== AUTHENTICATION INTEGRATION TEST =====");

      // Test 1: Check if AuthService is properly initialized
      await _testAuthServiceInitialization();

      // Test 2: Check if enhanced storage is working
      await _testEnhancedStorageIntegration();

      // Test 3: Check if token retrieval is working
      await _testTokenRetrieval();

      // Test 4: Check if API client can get tokens
      await _testApiClientTokenAccess();

      log("✅ ===== AUTHENTICATION INTEGRATION TEST COMPLETED =====");
    } catch (e) {
      log("❌ Authentication integration test failed: $e");
    }
  }

  static Future<void> _testAuthServiceInitialization() async {
    try {
      log("🧪 Testing AuthService initialization...");

      final authService = GetIt.I<AuthService>();
      final tokenManager = authService.tokenRefreshManager;

      log("✅ AuthService: ${authService.runtimeType}");
      log("✅ TokenManager: ${tokenManager.runtimeType}");

      // Test if token manager is initialized
      final accessToken = await authService.getValidAccessToken();
      log("🔑 Access token available: ${accessToken != null}");

      if (accessToken != null) {
        log("🔑 Access token length: ${accessToken.length} characters");
        log("🔑 Access token preview: ${accessToken.substring(0, 20)}...");
      }

      log("✅ AuthService initialization test passed");
    } catch (e) {
      log("❌ AuthService initialization test failed: $e");
    }
  }

  static Future<void> _testEnhancedStorageIntegration() async {
    try {
      log("🧪 Testing Enhanced Storage integration...");

      final enhancedStorage = EnhancedAuthStorage();

      // Check if enhanced tokens exist
      final enhancedTokens = await enhancedStorage.read();
      log("📦 Enhanced tokens available: ${enhancedTokens != null}");

      if (enhancedTokens != null) {
        log("📦 Enhanced tokens details:");
        log("   • Access token length: ${enhancedTokens.access.length}");
        log("   • Refresh token length: ${enhancedTokens.refresh.length}");
        log("   • Access expires at: ${enhancedTokens.accessExpiresAt}");
        log("   • Refresh expires at: ${enhancedTokens.refreshExpiresAt}");
        log("   • Time until access expiry: ${enhancedTokens.timeUntilAccessExpiry.inMinutes} minutes");
        log("   • Is access token expiring: ${enhancedTokens.isAccessTokenExpiring}");
        log("   • Dynamic buffer: ${enhancedTokens.dynamicAccessBuffer.inMinutes} minutes");
      }

      // Check storage statistics
      final stats = await enhancedStorage.getStorageStats();
      log("📊 Storage statistics:");
      log("   • Has enhanced tokens: ${stats['hasEnhancedTokens']}");
      log("   • Has legacy tokens: ${stats['hasLegacyTokens']}");
      log("   • Tokens valid: ${stats['tokensValid']}");

      log("✅ Enhanced Storage integration test passed");
    } catch (e) {
      log("❌ Enhanced Storage integration test failed: $e");
    }
  }

  static Future<void> _testTokenRetrieval() async {
    try {
      log("🧪 Testing token retrieval methods...");

      final authService = GetIt.I<AuthService>();
      final gateStorage = GateStorage();
      final enhancedStorage = EnhancedAuthStorage();

      // Test different token retrieval methods
      log("🔍 Testing different token retrieval methods:");

      // Method 1: AuthService.getValidAccessToken()
      final authServiceToken = await authService.getValidAccessToken();
      log("   • AuthService.getValidAccessToken(): ${authServiceToken != null ? 'SUCCESS' : 'FAILED'}");

      // Method 2: GateStorage.getAccessToken()
      final gateStorageToken = await gateStorage.getAccessToken();
      log("   • GateStorage.getAccessToken(): ${gateStorageToken != null ? 'SUCCESS' : 'FAILED'}");

      // Method 3: EnhancedAuthStorage.getAccessToken()
      final enhancedStorageToken = await enhancedStorage.getAccessToken();
      log("   • EnhancedAuthStorage.getAccessToken(): ${enhancedStorageToken != null ? 'SUCCESS' : 'FAILED'}");

      // Method 4: TokenRefreshManager.getValidAccessToken()
      final tokenManagerToken =
          await authService.tokenRefreshManager.getValidAccessToken();
      log("   • TokenRefreshManager.getValidAccessToken(): ${tokenManagerToken != null ? 'SUCCESS' : 'FAILED'}");

      // Compare tokens
      if (authServiceToken != null && gateStorageToken != null) {
        final tokensMatch = authServiceToken == gateStorageToken;
        log("🔍 AuthService vs GateStorage tokens match: $tokensMatch");
      }

      if (enhancedStorageToken != null && gateStorageToken != null) {
        final tokensMatch = enhancedStorageToken == gateStorageToken;
        log("🔍 Enhanced vs GateStorage tokens match: $tokensMatch");
      }

      log("✅ Token retrieval test passed");
    } catch (e) {
      log("❌ Token retrieval test failed: $e");
    }
  }

  static Future<void> _testApiClientTokenAccess() async {
    try {
      log("🧪 Testing API client token access...");

      final authService = GetIt.I<AuthService>();

      // Test the method that AuthenticatedApiClient uses
      final tokenManager = authService.tokenRefreshManager;
      final apiClientToken =
          await tokenManager.getValidAccessTokenWithImmediateRefresh();

      log("🌐 API client token access: ${apiClientToken != null ? 'SUCCESS' : 'FAILED'}");

      if (apiClientToken != null) {
        log("🌐 API client token length: ${apiClientToken.length} characters");
        log("🌐 API client token preview: ${apiClientToken.substring(0, 20)}...");
      } else {
        log("❌ API client cannot access tokens - this is the root cause of the 401 errors");

        // Diagnostic information
        log("🔍 Diagnostic information:");

        // Check if tokens exist in different storages
        final gateStorage = GateStorage();
        final enhancedStorage = EnhancedAuthStorage();

        final gateToken = await gateStorage.getAccessToken();
        final enhancedToken = await enhancedStorage.getAccessToken();

        log("   • GateStorage has token: ${gateToken != null}");
        log("   • EnhancedStorage has token: ${enhancedToken != null}");

        if (gateToken != null) {
          log("   • GateStorage token preview: ${gateToken.substring(0, 20)}...");
        }

        if (enhancedToken != null) {
          log("   • EnhancedStorage token preview: ${enhancedToken.substring(0, 20)}...");
        }
      }

      log("✅ API client token access test completed");
    } catch (e) {
      log("❌ API client token access test failed: $e");
    }
  }

  /// Quick diagnostic method to check authentication state
  static Future<void> quickDiagnostic() async {
    try {
      log("🔍 ===== QUICK AUTH DIAGNOSTIC =====");

      final authService = GetIt.I<AuthService>();
      final gateStorage = GateStorage();
      final enhancedStorage = EnhancedAuthStorage();

      // Check authentication status
      final isAuthenticated = await authService.isAuthenticated();
      log("🔐 Is authenticated: $isAuthenticated");

      // Check token availability
      final authServiceToken = await authService.getValidAccessToken();
      final gateStorageToken = await gateStorage.getAccessToken();
      final enhancedStorageToken = await enhancedStorage.getAccessToken();

      log("🔑 Token availability:");
      log("   • AuthService: ${authServiceToken != null}");
      log("   • GateStorage: ${gateStorageToken != null}");
      log("   • EnhancedStorage: ${enhancedStorageToken != null}");

      // Check API client access
      final apiToken = await authService.tokenRefreshManager
          .getValidAccessTokenWithImmediateRefresh();
      log("🌐 API client token access: ${apiToken != null}");

      if (!isAuthenticated || apiToken == null) {
        log("❌ ISSUE DETECTED: Authentication or token access problem");
        log("💡 SOLUTION: User may need to log in again or tokens need to be refreshed");
      } else {
        log("✅ Authentication system is working correctly");
      }

      log("🔍 ================================");
    } catch (e) {
      log("❌ Quick diagnostic failed: $e");
    }
  }

  /// Force token refresh and test
  static Future<void> testTokenRefresh() async {
    try {
      log("🔄 ===== TESTING TOKEN REFRESH =====");

      final authService = GetIt.I<AuthService>();

      log("🔄 Attempting token refresh...");
      final refreshSuccess = await authService.refreshToken();

      log("🔄 Token refresh result: ${refreshSuccess ? 'SUCCESS' : 'FAILED'}");

      if (refreshSuccess) {
        // Test token access after refresh
        final newToken = await authService.getValidAccessToken();
        log("🔑 New token available: ${newToken != null}");

        if (newToken != null) {
          log("🔑 New token preview: ${newToken.substring(0, 20)}...");
        }
      }

      log("🔄 ===============================");
    } catch (e) {
      log("❌ Token refresh test failed: $e");
    }
  }
}
