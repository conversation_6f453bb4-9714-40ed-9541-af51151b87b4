import 'dart:async';
import 'dart:developer';
import 'package:get_it/get_it.dart';
import 'package:flutter_onegate/services/auth_service/auth_service.dart';
import 'package:flutter_onegate/services/auth_service/jwt_token_utility.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

/// Diagnostic tool to identify refresh token expiration issues
class RefreshTokenDiagnostic {
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();
  static const String _accessTokenKey = 'access_token_secure';
  static const String _refreshTokenKey = 'refresh_token_secure';

  /// Comprehensive refresh token analysis
  static Future<Map<String, dynamic>> analyzeRefreshTokenIssue() async {
    try {
      log("🔍 Starting comprehensive refresh token analysis...");

      final analysis = <String, dynamic>{
        'timestamp': DateTime.now().toIso8601String(),
        'accessToken': {},
        'refreshToken': {},
        'issue': null,
        'recommendation': null,
        'keycloakConfig': {},
      };

      // Get tokens
      final accessToken = await _secureStorage.read(key: _accessTokenKey);
      final refreshToken = await _secureStorage.read(key: _refreshTokenKey);

      if (accessToken == null || refreshToken == null) {
        analysis['issue'] = 'Missing tokens';
        analysis['recommendation'] = 'User needs to login again';
        return analysis;
      }

      // Analyze access token
      analysis['accessToken'] = _analyzeToken(accessToken, 'ACCESS');

      // Analyze refresh token
      analysis['refreshToken'] = _analyzeToken(refreshToken, 'REFRESH');

      // Identify the issue
      final issue = _identifyIssue(analysis['accessToken'], analysis['refreshToken']);
      analysis['issue'] = issue['type'];
      analysis['recommendation'] = issue['recommendation'];

      // Log detailed analysis
      _logDetailedAnalysis(analysis);

      return analysis;
    } catch (e) {
      log("❌ Error during refresh token analysis: $e");
      return {
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Analyze individual token
  static Map<String, dynamic> _analyzeToken(String token, String type) {
    try {
      final payload = JwtTokenUtility.parseJwtToken(token);
      if (payload == null) {
        return {
          'type': type,
          'valid': false,
          'error': 'Invalid JWT format',
        };
      }

      final iat = payload['iat'];
      final exp = payload['exp'];
      final now = DateTime.now();

      if (iat == null || exp == null) {
        return {
          'type': type,
          'valid': false,
          'error': 'Missing iat or exp claims',
        };
      }

      final issuedAt = DateTime.fromMillisecondsSinceEpoch(iat * 1000);
      final expiresAt = DateTime.fromMillisecondsSinceEpoch(exp * 1000);
      final lifespan = expiresAt.difference(issuedAt);
      final timeUntilExpiry = expiresAt.difference(now);
      final isExpired = now.isAfter(expiresAt);

      return {
        'type': type,
        'valid': true,
        'issuedAt': issuedAt.toIso8601String(),
        'expiresAt': expiresAt.toIso8601String(),
        'lifespanMinutes': lifespan.inMinutes,
        'timeUntilExpiryMinutes': timeUntilExpiry.inMinutes,
        'isExpired': isExpired,
        'preview': token.substring(0, 20) + '...',
      };
    } catch (e) {
      return {
        'type': type,
        'valid': false,
        'error': e.toString(),
      };
    }
  }

  /// Identify the specific issue
  static Map<String, String> _identifyIssue(
    Map<String, dynamic> accessAnalysis,
    Map<String, dynamic> refreshAnalysis,
  ) {
    // Check if tokens are valid
    if (!accessAnalysis['valid'] || !refreshAnalysis['valid']) {
      return {
        'type': 'Invalid Token Format',
        'recommendation': 'Tokens are malformed. User needs to login again.',
      };
    }

    final accessLifespan = accessAnalysis['lifespanMinutes'] as int;
    final refreshLifespan = refreshAnalysis['lifespanMinutes'] as int;
    final accessExpired = accessAnalysis['isExpired'] as bool;
    final refreshExpired = refreshAnalysis['isExpired'] as bool;

    // Case 1: Both tokens expired
    if (accessExpired && refreshExpired) {
      return {
        'type': 'Both Tokens Expired',
        'recommendation': 'Both access and refresh tokens are expired. User needs to login again.',
      };
    }

    // Case 2: Refresh token expired but access token valid
    if (refreshExpired && !accessExpired) {
      return {
        'type': 'Refresh Token Expired Early',
        'recommendation': 'Refresh token expired before access token. This breaks continuous refresh. Increase refresh token lifespan in Keycloak.',
      };
    }

    // Case 3: Same lifespan issue (your exact problem)
    if (accessLifespan == refreshLifespan) {
      return {
        'type': 'Same Token Lifespan Issue',
        'recommendation': 'Access and refresh tokens have the same lifespan ($accessLifespan min). This prevents continuous refresh. Set refresh token lifespan to at least 6x access token lifespan in Keycloak.',
      };
    }

    // Case 4: Refresh token too short
    if (refreshLifespan < accessLifespan * 3) {
      return {
        'type': 'Refresh Token Too Short',
        'recommendation': 'Refresh token lifespan ($refreshLifespan min) is too short compared to access token ($accessLifespan min). Increase refresh token lifespan to at least ${accessLifespan * 6} minutes in Keycloak.',
      };
    }

    // Case 5: Access token about to expire
    final accessTimeLeft = accessAnalysis['timeUntilExpiryMinutes'] as int;
    if (accessTimeLeft <= 2 && !accessExpired) {
      return {
        'type': 'Access Token Expiring Soon',
        'recommendation': 'Access token expires in $accessTimeLeft minutes. Automatic refresh should trigger soon.',
      };
    }

    // Case 6: Everything looks good
    return {
      'type': 'Configuration Looks Good',
      'recommendation': 'Token configuration appears correct. Access: ${accessLifespan}min, Refresh: ${refreshLifespan}min.',
    };
  }

  /// Log detailed analysis
  static void _logDetailedAnalysis(Map<String, dynamic> analysis) {
    log("📊 REFRESH TOKEN DIAGNOSTIC REPORT");
    log("=" * 50);
    
    if (analysis['accessToken']['valid'] == true) {
      final access = analysis['accessToken'];
      log("🔑 ACCESS TOKEN:");
      log("   • Issued: ${access['issuedAt']}");
      log("   • Expires: ${access['expiresAt']}");
      log("   • Lifespan: ${access['lifespanMinutes']} minutes");
      log("   • Time Left: ${access['timeUntilExpiryMinutes']} minutes");
      log("   • Expired: ${access['isExpired']}");
    }

    if (analysis['refreshToken']['valid'] == true) {
      final refresh = analysis['refreshToken'];
      log("🔄 REFRESH TOKEN:");
      log("   • Issued: ${refresh['issuedAt']}");
      log("   • Expires: ${refresh['expiresAt']}");
      log("   • Lifespan: ${refresh['lifespanMinutes']} minutes");
      log("   • Time Left: ${refresh['timeUntilExpiryMinutes']} minutes");
      log("   • Expired: ${refresh['isExpired']}");
    }

    log("🚨 ISSUE: ${analysis['issue']}");
    log("💡 RECOMMENDATION: ${analysis['recommendation']}");
    log("=" * 50);
  }

  /// Monitor refresh token behavior over time
  static Timer? _monitoringTimer;
  static bool _isMonitoring = false;

  static Future<void> startRefreshTokenMonitoring({
    Duration interval = const Duration(minutes: 1),
  }) async {
    if (_isMonitoring) {
      log("⚠️ Refresh token monitoring already running");
      return;
    }

    _isMonitoring = true;
    log("🔍 Starting refresh token monitoring (every ${interval.inMinutes} minutes)");

    _monitoringTimer = Timer.periodic(interval, (timer) async {
      final analysis = await analyzeRefreshTokenIssue();
      
      if (analysis['issue'] != 'Configuration Looks Good') {
        log("🚨 REFRESH TOKEN ISSUE DETECTED: ${analysis['issue']}");
        log("💡 ${analysis['recommendation']}");
      }
    });
  }

  static void stopRefreshTokenMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
    _isMonitoring = false;
    log("🛑 Refresh token monitoring stopped");
  }

  /// Generate Keycloak configuration recommendations
  static Map<String, dynamic> generateKeycloakRecommendations(
    Map<String, dynamic> analysis,
  ) {
    if (analysis['accessToken']['valid'] != true) {
      return {'error': 'Cannot generate recommendations without valid tokens'};
    }

    final accessLifespan = analysis['accessToken']['lifespanMinutes'] as int;
    final recommendedRefreshLifespan = accessLifespan * 6; // 6x access token

    return {
      'current': {
        'access_token_lifespan': '${accessLifespan}m',
        'refresh_token_lifespan': '${analysis['refreshToken']['lifespanMinutes']}m',
      },
      'recommended': {
        'access_token_lifespan': '${accessLifespan}m', // Keep current
        'refresh_token_lifespan': '${recommendedRefreshLifespan}m', // Increase
        'refresh_token_lifespan_for_web': '${recommendedRefreshLifespan}m',
        'sso_session_idle_timeout': '${recommendedRefreshLifespan}m',
        'sso_session_max_lifespan': '12h',
      },
      'explanation': {
        'access_token': 'Keep short for security (${accessLifespan} minutes)',
        'refresh_token': 'Increase to ${recommendedRefreshLifespan} minutes to allow ${recommendedRefreshLifespan ~/ accessLifespan} refresh cycles',
        'session_timeout': 'Match refresh token lifespan',
        'max_session': 'Allow long sessions for better UX',
      }
    };
  }
}
