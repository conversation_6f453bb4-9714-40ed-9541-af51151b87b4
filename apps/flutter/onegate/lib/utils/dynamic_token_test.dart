import 'dart:developer';
import 'package:flutter_onegate/services/auth_service/auth_service.dart';
import 'package:flutter_onegate/services/auth_service/enhanced_token_refresh_manager.dart';
import 'package:flutter_onegate/services/auth_service/dynamic_token_scheduler.dart';
import 'package:flutter_onegate/services/auth_service/dynamic_auth_integration.dart';
import 'package:flutter_onegate/services/auth_service/jwt_token_utility.dart';
import 'package:get_it/get_it.dart';

/// Comprehensive test utility for dynamic token expiration handling
class DynamicTokenTest {
  static final DynamicTokenTest _instance = DynamicTokenTest._internal();
  factory DynamicTokenTest() => _instance;
  DynamicTokenTest._internal();

  /// Test dynamic token expiration handling system
  static Future<void> testDynamicTokenSystem() async {
    try {
      log("🧪 ===== DYNAMIC TOKEN SYSTEM TEST =====");
      
      // Test 1: JWT Token Utility Dynamic Analysis
      await _testJwtTokenUtilityDynamicAnalysis();
      
      // Test 2: Enhanced Token Refresh Manager
      await _testEnhancedTokenRefreshManager();
      
      // Test 3: Dynamic Token Scheduler
      await _testDynamicTokenScheduler();
      
      // Test 4: Dynamic Auth Integration
      await _testDynamicAuthIntegration();
      
      // Test 5: End-to-End Token Flow
      await _testEndToEndTokenFlow();
      
      log("✅ ===== DYNAMIC TOKEN SYSTEM TEST COMPLETED =====");
    } catch (e) {
      log("❌ Dynamic token system test failed: $e");
    }
  }

  /// Test JWT Token Utility dynamic analysis capabilities
  static Future<void> _testJwtTokenUtilityDynamicAnalysis() async {
    try {
      log("🧪 Testing JWT Token Utility Dynamic Analysis...");
      
      // Get current access token
      final authService = GetIt.I<AuthService>();
      final accessToken = await authService.getValidAccessToken();
      
      if (accessToken == null) {
        log("⚠️ No access token available for testing");
        return;
      }
      
      // Test token analysis
      final analysis = JwtTokenUtility.getTokenAnalysis(accessToken);
      log("📊 Token Analysis Results:");
      log("   • Lifespan: ${analysis['lifespanMinutes']} minutes");
      log("   • Refresh Buffer: ${analysis['refreshBuffer']} minutes");
      log("   • Should Refresh Now: ${analysis['shouldRefreshNow']}");
      log("   • Time Until Expiry: ${analysis['timeUntilExpiryMinutes']} minutes");
      
      // Test optimal refresh time calculation
      final refreshTime = JwtTokenUtility.getOptimalRefreshTime(accessToken);
      if (refreshTime != null) {
        final timeUntilRefresh = refreshTime.difference(DateTime.now());
        log("⏰ Optimal refresh time: $refreshTime (in ${timeUntilRefresh.inMinutes} minutes)");
      }
      
      // Test dynamic buffer calculation
      final buffer = JwtTokenUtility.calculateOptimalRefreshBuffer(accessToken);
      log("🔄 Dynamic refresh buffer: ${buffer.inMinutes} minutes");
      
      log("✅ JWT Token Utility Dynamic Analysis test passed");
    } catch (e) {
      log("❌ JWT Token Utility Dynamic Analysis test failed: $e");
    }
  }

  /// Test Enhanced Token Refresh Manager
  static Future<void> _testEnhancedTokenRefreshManager() async {
    try {
      log("🧪 Testing Enhanced Token Refresh Manager...");
      
      final authService = GetIt.I<AuthService>();
      final tokenManager = authService.tokenRefreshManager;
      
      // Test dynamic buffer calculation
      final accessToken = await tokenManager.getValidAccessToken();
      if (accessToken != null) {
        final buffer = tokenManager.calculateDynamicRefreshBuffer(accessToken);
        log("📊 Dynamic refresh buffer: ${buffer.inMinutes} minutes");
        
        // Test current refresh buffer
        final currentBuffer = tokenManager.currentRefreshBuffer;
        log("🔄 Current refresh buffer: ${currentBuffer.inMinutes} minutes");
      }
      
      log("✅ Enhanced Token Refresh Manager test passed");
    } catch (e) {
      log("❌ Enhanced Token Refresh Manager test failed: $e");
    }
  }

  /// Test Dynamic Token Scheduler
  static Future<void> _testDynamicTokenScheduler() async {
    try {
      log("🧪 Testing Dynamic Token Scheduler...");
      
      final authService = GetIt.I<AuthService>();
      final tokenManager = authService.tokenRefreshManager;
      
      // Create and initialize dynamic token scheduler
      final scheduler = DynamicTokenScheduler();
      await scheduler.initialize(tokenManager);
      
      // Test scheduling status
      final status = scheduler.getSchedulingStatus();
      log("📊 Scheduler Status:");
      log("   • Is Initialized: ${status['isInitialized']}");
      log("   • Is Active: ${status['isActive']}");
      log("   • Monitoring Interval: ${status['monitoringIntervalSeconds']}s");
      
      // Start dynamic scheduling
      await scheduler.startDynamicScheduling();
      
      // Check status after starting
      final activeStatus = scheduler.getSchedulingStatus();
      log("🔄 Active Scheduler Status:");
      log("   • Is Active: ${activeStatus['isActive']}");
      log("   • Has Access Token Timer: ${activeStatus['hasAccessTokenTimer']}");
      log("   • Next Access Token Refresh: ${activeStatus['nextAccessTokenRefresh']}");
      
      // Test force token analysis
      await scheduler.forceTokenAnalysis();
      log("🔍 Force token analysis completed");
      
      // Stop scheduling
      scheduler.stopDynamicScheduling();
      log("🛑 Dynamic scheduling stopped");
      
      log("✅ Dynamic Token Scheduler test passed");
    } catch (e) {
      log("❌ Dynamic Token Scheduler test failed: $e");
    }
  }

  /// Test Dynamic Auth Integration
  static Future<void> _testDynamicAuthIntegration() async {
    try {
      log("🧪 Testing Dynamic Auth Integration...");
      
      final authService = GetIt.I<AuthService>();
      final dynamicAuth = DynamicAuthIntegration();
      
      // Initialize dynamic auth integration
      await dynamicAuth.initialize(authService);
      
      // Test enhanced access token retrieval
      final accessToken = await dynamicAuth.getEnhancedAccessToken();
      if (accessToken != null) {
        log("🔑 Enhanced access token retrieved successfully");
        log("   • Token length: ${accessToken.length} characters");
      }
      
      // Test comprehensive auth status
      final status = dynamicAuth.getComprehensiveAuthStatus();
      log("📊 Comprehensive Auth Status:");
      log("   • Dynamic Auth Integration: ${status['dynamicAuthIntegration']}");
      log("   • Dynamic Token Scheduler: ${status['dynamicTokenScheduler']}");
      log("   • Token Refresh Manager: ${status['tokenRefreshManager']}");
      
      // Test token expiration info
      final expirationInfo = await dynamicAuth.getTokenExpirationInfo();
      if (expirationInfo != null) {
        log("⏰ Token Expiration Info:");
        log("   • Lifespan: ${expirationInfo['lifespanMinutes']} minutes");
        log("   • Should Refresh: ${expirationInfo['shouldRefreshNow']}");
      }
      
      // Test authentication validation
      final isAuthenticated = await dynamicAuth.isAuthenticatedWithDynamicValidation();
      log("🔐 Is Authenticated: $isAuthenticated");
      
      log("✅ Dynamic Auth Integration test passed");
    } catch (e) {
      log("❌ Dynamic Auth Integration test failed: $e");
    }
  }

  /// Test end-to-end token flow
  static Future<void> _testEndToEndTokenFlow() async {
    try {
      log("🧪 Testing End-to-End Token Flow...");
      
      final authService = GetIt.I<AuthService>();
      final dynamicAuth = DynamicAuthIntegration();
      
      // Initialize dynamic auth
      await dynamicAuth.initialize(authService);
      
      // Test token retrieval flow
      log("🔄 Testing token retrieval flow...");
      final token1 = await dynamicAuth.getEnhancedAccessToken();
      log("   • First token retrieval: ${token1 != null ? 'Success' : 'Failed'}");
      
      // Test immediate token retrieval (should use cache/existing token)
      final token2 = await dynamicAuth.getEnhancedAccessToken();
      log("   • Second token retrieval: ${token2 != null ? 'Success' : 'Failed'}");
      log("   • Tokens match: ${token1 == token2}");
      
      // Test comprehensive refresh
      log("🔄 Testing comprehensive refresh...");
      final refreshSuccess = await dynamicAuth.forceComprehensiveRefresh();
      log("   • Comprehensive refresh: ${refreshSuccess ? 'Success' : 'Failed'}");
      
      // Test token after refresh
      final token3 = await dynamicAuth.getEnhancedAccessToken();
      log("   • Token after refresh: ${token3 != null ? 'Success' : 'Failed'}");
      
      // Test dynamic refresh buffer
      final currentBuffer = await dynamicAuth.getCurrentDynamicRefreshBuffer();
      if (currentBuffer != null) {
        log("🔄 Current dynamic refresh buffer: ${currentBuffer.inMinutes} minutes");
      }
      
      // Test should refresh check
      final shouldRefresh = await dynamicAuth.shouldRefreshTokenNow();
      log("🔍 Should refresh token now: $shouldRefresh");
      
      log("✅ End-to-End Token Flow test passed");
    } catch (e) {
      log("❌ End-to-End Token Flow test failed: $e");
    }
  }

  /// Test token refresh timing accuracy
  static Future<void> testTokenRefreshTiming() async {
    try {
      log("🧪 ===== TOKEN REFRESH TIMING TEST =====");
      
      final authService = GetIt.I<AuthService>();
      final accessToken = await authService.getValidAccessToken();
      
      if (accessToken == null) {
        log("⚠️ No access token available for timing test");
        return;
      }
      
      // Get token timing information
      final issuedAt = JwtTokenUtility.getTokenIssuedAtTime(accessToken);
      final expiresAt = JwtTokenUtility.getTokenExpirationTime(accessToken);
      final lifespan = JwtTokenUtility.getTokenLifespanInMinutes(accessToken);
      final timeUntilExpiry = JwtTokenUtility.getTimeUntilExpiration(accessToken);
      final optimalRefreshTime = JwtTokenUtility.getOptimalRefreshTime(accessToken);
      
      log("⏰ Token Timing Analysis:");
      log("   • Issued At: $issuedAt");
      log("   • Expires At: $expiresAt");
      log("   • Lifespan: $lifespan minutes");
      log("   • Time Until Expiry: ${timeUntilExpiry?.inMinutes} minutes");
      log("   • Optimal Refresh Time: $optimalRefreshTime");
      
      if (optimalRefreshTime != null) {
        final timeUntilRefresh = optimalRefreshTime.difference(DateTime.now());
        log("   • Time Until Optimal Refresh: ${timeUntilRefresh.inMinutes} minutes");
        
        // Calculate refresh efficiency
        if (timeUntilExpiry != null) {
          final refreshEfficiency = (timeUntilRefresh.inMinutes / timeUntilExpiry.inMinutes) * 100;
          log("   • Refresh Efficiency: ${refreshEfficiency.toStringAsFixed(1)}%");
        }
      }
      
      log("✅ ===== TOKEN REFRESH TIMING TEST COMPLETED =====");
    } catch (e) {
      log("❌ Token refresh timing test failed: $e");
    }
  }

  /// Log comprehensive system status
  static Future<void> logSystemStatus() async {
    try {
      log("📊 ===== DYNAMIC TOKEN SYSTEM STATUS =====");
      
      final authService = GetIt.I<AuthService>();
      final dynamicAuth = DynamicAuthIntegration();
      
      // Initialize if needed
      await dynamicAuth.initialize(authService);
      
      // Log comprehensive status
      await dynamicAuth.logComprehensiveTokenDetails();
      
      log("📊 ========================================");
    } catch (e) {
      log("❌ Error logging system status: $e");
    }
  }
}
