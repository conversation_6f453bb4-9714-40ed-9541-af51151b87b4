import 'dart:async';
import 'dart:developer';
import 'package:get_it/get_it.dart';
import 'package:flutter_onegate/services/auth_service/auth_service.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

/// Utility to detect and analyze session timeouts (like the 9:48 AM issue)
class SessionTimeoutDetector {
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();
  static const String _loginTimeKey = 'login_timestamp';
  static Timer? _monitoringTimer;
  static bool _isMonitoring = false;
  static DateTime? _loginTime;

  /// Start monitoring session for timeout detection
  static Future<void> startSessionMonitoring() async {
    if (_isMonitoring) {
      log("⚠️ Session monitoring already running");
      return;
    }

    _isMonitoring = true;
    
    // Record login time
    _loginTime = DateTime.now();
    await _secureStorage.write(key: _loginTimeKey, value: _loginTime!.toIso8601String());
    
    log("🔍 Starting session timeout monitoring...");
    log("📅 Login Time: ${_loginTime!.toLocal().toString()}");
    
    // Monitor every minute
    _monitoringTimer = Timer.periodic(const Duration(minutes: 1), (timer) async {
      await _checkSessionStatus();
    });
  }

  /// Stop session monitoring
  static void stopSessionMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
    _isMonitoring = false;
    _loginTime = null;
    log("🛑 Session timeout monitoring stopped");
  }

  /// Check current session status and detect potential timeouts
  static Future<void> _checkSessionStatus() async {
    try {
      if (_loginTime == null) {
        final loginTimeStr = await _secureStorage.read(key: _loginTimeKey);
        if (loginTimeStr != null) {
          _loginTime = DateTime.parse(loginTimeStr);
        } else {
          log("⚠️ No login time recorded");
          return;
        }
      }

      final now = DateTime.now();
      final sessionDuration = now.difference(_loginTime!);
      final sessionMinutes = sessionDuration.inMinutes;

      // Check authentication status
      final authService = GetIt.I<AuthService>();
      final isAuthenticated = await authService.isAuthenticated();

      log("📊 SESSION STATUS CHECK:");
      log("   • Session Duration: ${sessionMinutes} minutes");
      log("   • Is Authenticated: $isAuthenticated");

      // Check for critical session durations
      _checkCriticalDurations(sessionMinutes, isAuthenticated);

      // If session is lost, analyze the cause
      if (!isAuthenticated) {
        await _analyzeSessionLoss(sessionMinutes);
      }

    } catch (e) {
      log("❌ Error checking session status: $e");
    }
  }

  /// Check for critical session durations that might indicate timeout patterns
  static void _checkCriticalDurations(int sessionMinutes, bool isAuthenticated) {
    // Common Keycloak timeout patterns
    final criticalDurations = [
      30,  // 30 minutes - common SSO idle timeout
      45,  // 45 minutes - common client session timeout
      48,  // 48 minutes - your specific issue
      60,  // 1 hour - common max session
      90,  // 1.5 hours - extended session
      120, // 2 hours - long session
    ];

    for (final duration in criticalDurations) {
      if (sessionMinutes == duration) {
        if (isAuthenticated) {
          log("✅ Session survived ${duration}-minute mark");
        } else {
          log("🚨 SESSION LOST at exactly ${duration} minutes!");
          log("💡 This suggests a Keycloak timeout configuration issue");
        }
      } else if (sessionMinutes == duration - 1) {
        log("⚠️ Approaching ${duration}-minute mark - monitoring closely...");
      }
    }

    // Special attention to the 48-minute issue
    if (sessionMinutes >= 45 && sessionMinutes <= 50) {
      if (sessionMinutes == 48 && !isAuthenticated) {
        log("🎯 CONFIRMED: 48-minute session timeout detected!");
        log("🔧 This is likely sso_session_max_lifespan in Keycloak");
      } else if (isAuthenticated) {
        log("👀 Monitoring 48-minute window... (currently ${sessionMinutes} min)");
      }
    }
  }

  /// Analyze the cause of session loss
  static Future<void> _analyzeSessionLoss(int sessionMinutes) async {
    log("🔍 ANALYZING SESSION LOSS:");
    log("   • Session lasted: ${sessionMinutes} minutes");

    // Determine likely cause based on duration
    String likelyCause;
    String keycloakSetting;
    String recommendation;

    if (sessionMinutes <= 5) {
      likelyCause = "Immediate token expiry";
      keycloakSetting = "access_token_lifespan";
      recommendation = "Check if access tokens are expiring without refresh";
    } else if (sessionMinutes <= 30) {
      likelyCause = "Refresh token expiry";
      keycloakSetting = "refresh_token_lifespan";
      recommendation = "Increase refresh token lifespan to 30+ minutes";
    } else if (sessionMinutes >= 45 && sessionMinutes <= 50) {
      likelyCause = "SSO Session timeout";
      keycloakSetting = "sso_session_max_lifespan";
      recommendation = "Increase SSO session max lifespan to 24+ hours";
    } else if (sessionMinutes >= 55 && sessionMinutes <= 65) {
      likelyCause = "Client session timeout";
      keycloakSetting = "client_session_max_lifespan";
      recommendation = "Increase client session max lifespan";
    } else {
      likelyCause = "Unknown timeout mechanism";
      keycloakSetting = "Multiple settings";
      recommendation = "Check all Keycloak session and token settings";
    }

    log("💡 DIAGNOSIS:");
    log("   • Likely Cause: $likelyCause");
    log("   • Keycloak Setting: $keycloakSetting");
    log("   • Recommendation: $recommendation");

    // Generate specific Keycloak configuration
    _generateKeycloakFix(sessionMinutes);
  }

  /// Generate specific Keycloak configuration to fix the timeout
  static void _generateKeycloakFix(int sessionMinutes) {
    log("🔧 KEYCLOAK CONFIGURATION FIX:");

    if (sessionMinutes >= 45 && sessionMinutes <= 50) {
      // 48-minute issue fix
      log("   • Problem: SSO session expires at ~48 minutes");
      log("   • Fix: Update Keycloak Realm Settings:");
      log("     - sso_session_max_lifespan: 24h (currently ~48m)");
      log("     - sso_session_idle_timeout: 12h");
      log("     - client_session_max_lifespan: 24h");
      log("     - offline_session_max_lifespan: 720h (30 days)");
    } else if (sessionMinutes <= 30) {
      // Token-level issue
      log("   • Problem: Token refresh cycle broken");
      log("   • Fix: Update Client Settings:");
      log("     - access_token_lifespan: 5m");
      log("     - refresh_token_lifespan: 30m (currently too short)");
      log("     - refresh_token_max_reuse: 0");
      log("     - revoke_refresh_token: true");
    } else {
      // General session issue
      log("   • Problem: Session timeout at ${sessionMinutes} minutes");
      log("   • Fix: Review all Keycloak session settings");
      log("     - Check realm-level session timeouts");
      log("     - Check client-level session timeouts");
      log("     - Ensure token refresh is working properly");
    }
  }

  /// Get current session information
  static Future<Map<String, dynamic>> getSessionInfo() async {
    try {
      final loginTimeStr = await _secureStorage.read(key: _loginTimeKey);
      if (loginTimeStr == null) {
        return {'error': 'No login time recorded'};
      }

      final loginTime = DateTime.parse(loginTimeStr);
      final now = DateTime.now();
      final sessionDuration = now.difference(loginTime);

      final authService = GetIt.I<AuthService>();
      final isAuthenticated = await authService.isAuthenticated();

      return {
        'loginTime': loginTime.toIso8601String(),
        'currentTime': now.toIso8601String(),
        'sessionDurationMinutes': sessionDuration.inMinutes,
        'sessionDurationSeconds': sessionDuration.inSeconds,
        'isAuthenticated': isAuthenticated,
        'isMonitoring': _isMonitoring,
      };
    } catch (e) {
      return {'error': e.toString()};
    }
  }

  /// Predict when session might expire based on patterns
  static Future<List<String>> predictSessionExpiry() async {
    final sessionInfo = await getSessionInfo();
    if (sessionInfo['error'] != null) {
      return ['Error getting session info: ${sessionInfo['error']}'];
    }

    final sessionMinutes = sessionInfo['sessionDurationMinutes'] as int;
    final predictions = <String>[];

    // Common expiry patterns
    final expiryPatterns = [
      {'minutes': 30, 'cause': 'SSO idle timeout'},
      {'minutes': 48, 'cause': 'SSO max lifespan (your issue)'},
      {'minutes': 60, 'cause': 'Client session timeout'},
      {'minutes': 90, 'cause': 'Extended session timeout'},
    ];

    for (final pattern in expiryPatterns) {
      final expiryMinutes = pattern['minutes'] as int;
      final cause = pattern['cause'] as String;

      if (sessionMinutes < expiryMinutes) {
        final remainingMinutes = expiryMinutes - sessionMinutes;
        predictions.add('⚠️ Possible expiry in ${remainingMinutes} minutes (${cause})');
      }
    }

    if (predictions.isEmpty) {
      predictions.add('✅ Session has survived common timeout patterns');
    }

    return predictions;
  }
}
