import 'dart:developer';

/// Helper utility to format and convert token expiration times
class TimeFormatHelper {
  
  /// Convert UTC time to local time with readable format
  static String formatTokenExpiry(DateTime utcTime) {
    final localTime = utcTime.toLocal();
    final now = DateTime.now();
    final difference = utcTime.difference(now);
    
    final formattedLocal = _formatDateTime(localTime);
    final formattedUtc = _formatDateTime(utcTime.toUtc());
    
    String timeUntilExpiry;
    if (difference.isNegative) {
      timeUntilExpiry = "EXPIRED ${difference.abs().inMinutes} minutes ago";
    } else {
      timeUntilExpiry = "${difference.inMinutes} minutes from now";
    }
    
    return """
🕒 Token Expires:
   • Local Time: $formattedLocal
   • UTC Time: $formattedUtc
   • Time Until Expiry: $timeUntilExpiry""";
  }
  
  /// Format DateTime to readable string
  static String _formatDateTime(DateTime dateTime) {
    final weekday = _getWeekdayName(dateTime.weekday);
    final month = _getMonthName(dateTime.month);
    final day = dateTime.day.toString().padLeft(2, '0');
    final year = dateTime.year;
    final hour = dateTime.hour.toString().padLeft(2, '0');
    final minute = dateTime.minute.toString().padLeft(2, '0');
    final second = dateTime.second.toString().padLeft(2, '0');
    
    return "$weekday, $month $day, $year at $hour:$minute:$second";
  }
  
  /// Get weekday name
  static String _getWeekdayName(int weekday) {
    const weekdays = [
      'Monday', 'Tuesday', 'Wednesday', 'Thursday', 
      'Friday', 'Saturday', 'Sunday'
    ];
    return weekdays[weekday - 1];
  }
  
  /// Get month name
  static String _getMonthName(int month) {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[month - 1];
  }
  
  /// Log token expiration in multiple formats
  static void logTokenExpiration(String tokenType, DateTime expiryTime) {
    log("🕒 $tokenType TOKEN EXPIRATION:");
    log("   • ISO 8601 (UTC): ${expiryTime.toUtc().toIso8601String()}");
    log("   • Local Time: ${_formatDateTime(expiryTime.toLocal())}");
    log("   • UTC Time: ${_formatDateTime(expiryTime.toUtc())}");
    
    final now = DateTime.now();
    final difference = expiryTime.difference(now);
    
    if (difference.isNegative) {
      log("   • Status: ❌ EXPIRED ${difference.abs().inMinutes} minutes ago");
    } else {
      log("   • Status: ✅ Valid for ${difference.inMinutes} minutes");
      
      if (difference.inMinutes <= 2) {
        log("   • Warning: 🚨 Expires very soon!");
      } else if (difference.inMinutes <= 5) {
        log("   • Warning: ⚠️ Expires soon");
      }
    }
  }
  
  /// Convert ISO 8601 string to readable format
  static String convertIsoToReadable(String isoString) {
    try {
      final dateTime = DateTime.parse(isoString);
      return formatTokenExpiry(dateTime);
    } catch (e) {
      return "❌ Invalid time format: $isoString";
    }
  }
  
  /// Get current time in multiple formats for comparison
  static void logCurrentTime() {
    final now = DateTime.now();
    log("🕐 CURRENT TIME:");
    log("   • Local: ${_formatDateTime(now)}");
    log("   • UTC: ${_formatDateTime(now.toUtc())}");
    log("   • ISO 8601: ${now.toUtc().toIso8601String()}");
  }
  
  /// Compare two times and show the difference
  static void compareTokenTimes(DateTime accessExpiry, DateTime refreshExpiry) {
    log("⚖️ TOKEN TIME COMPARISON:");
    
    logTokenExpiration("ACCESS", accessExpiry);
    logTokenExpiration("REFRESH", refreshExpiry);
    
    final difference = refreshExpiry.difference(accessExpiry);
    log("📊 COMPARISON RESULT:");
    
    if (difference.isNegative) {
      log("   • 🚨 CRITICAL: Refresh token expires ${difference.abs().inMinutes} minutes BEFORE access token!");
      log("   • 💡 This will break token refresh - fix Keycloak configuration");
    } else if (difference.inMinutes == 0) {
      log("   • 🚨 WARNING: Both tokens expire at the same time!");
      log("   • 💡 Refresh token should expire much later than access token");
    } else {
      log("   • ✅ Refresh token expires ${difference.inMinutes} minutes AFTER access token");
      
      final ratio = refreshExpiry.difference(DateTime.now()).inMinutes / 
                   accessExpiry.difference(DateTime.now()).inMinutes;
      
      if (ratio < 2.0) {
        log("   • ⚠️ WARNING: Refresh token lifespan ratio is ${ratio.toStringAsFixed(1)}x (recommended: 6x+)");
      } else if (ratio >= 6.0) {
        log("   • ✅ EXCELLENT: Refresh token lifespan ratio is ${ratio.toStringAsFixed(1)}x");
      } else {
        log("   • 👍 GOOD: Refresh token lifespan ratio is ${ratio.toStringAsFixed(1)}x (could be better)");
      }
    }
  }
  
  /// Explain time format for debugging
  static void explainTimeFormat(String isoString) {
    log("🔍 TIME FORMAT EXPLANATION:");
    log("   • Input: $isoString");
    
    try {
      final parts = isoString.split('T');
      if (parts.length == 2) {
        final datePart = parts[0];
        final timePart = parts[1].replaceAll('Z', '');
        
        final dateComponents = datePart.split('-');
        final timeComponents = timePart.split(':');
        
        log("   • Date Part: $datePart");
        log("     - Year: ${dateComponents[0]}");
        log("     - Month: ${dateComponents[1]}");
        log("     - Day: ${dateComponents[2]}");
        
        log("   • Time Part: $timePart");
        log("     - Hour: ${timeComponents[0]} (24-hour format)");
        log("     - Minute: ${timeComponents[1]}");
        log("     - Second: ${timeComponents[2].split('.')[0]}");
        
        if (isoString.endsWith('Z')) {
          log("   • Timezone: Z = UTC (Universal Coordinated Time)");
        }
        
        final dateTime = DateTime.parse(isoString);
        log("   • Converted to Local: ${_formatDateTime(dateTime.toLocal())}");
      }
    } catch (e) {
      log("   • ❌ Error parsing time format: $e");
    }
  }
}

/// Extension to add time formatting to DateTime
extension DateTimeFormatting on DateTime {
  /// Get readable format
  String get readable => TimeFormatHelper._formatDateTime(this);
  
  /// Get time until expiry
  String get timeUntilExpiry {
    final difference = this.difference(DateTime.now());
    if (difference.isNegative) {
      return "EXPIRED ${difference.abs().inMinutes} minutes ago";
    } else {
      return "${difference.inMinutes} minutes from now";
    }
  }
}
