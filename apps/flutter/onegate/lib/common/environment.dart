import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_onegate/services/auth_service/auth_service.dart';
import 'dart:developer';

class Environment {
  /// Retrieve headers with access token for API requests
  /// Uses multiple fallback methods to ensure token availability
  static Future<Map<String, String>> getHeaders() async {
    String? accessToken;

    try {
      // Method 1: Try enhanced authentication system
      final authService = GetIt.I<AuthService>();
      accessToken = await authService.getValidAccessToken();

      if (accessToken != null && accessToken.isNotEmpty) {
        log('🔑 Environment.getHeaders() using enhanced auth system');
        return {
          'Authorization': 'Bearer $accessToken',
          'Content-Type': 'application/json',
        };
      }
    } catch (e) {
      log('⚠️ Enhanced auth system failed: $e');
    }

    try {
      // Method 2: Try direct token refresh manager
      final authService = GetIt.I<AuthService>();
      accessToken = await authService.tokenRefreshManager.getValidAccessToken();

      if (accessToken != null && accessToken.isNotEmpty) {
        log('🔑 Environment.getHeaders() using token refresh manager');
        return {
          'Authorization': 'Bearer $accessToken',
          'Content-Type': 'application/json',
        };
      }
    } catch (e) {
      log('⚠️ Token refresh manager failed: $e');
    }

    try {
      // Method 3: Try gate storage
      final authService = GetIt.I<AuthService>();
      accessToken = await authService.gateStorage.getAccessToken();

      if (accessToken != null && accessToken.isNotEmpty) {
        log('🔑 Environment.getHeaders() using gate storage');
        return {
          'Authorization': 'Bearer $accessToken',
          'Content-Type': 'application/json',
        };
      }
    } catch (e) {
      log('⚠️ Gate storage failed: $e');
    }

    try {
      // Method 4: Fallback to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      accessToken = prefs.getString('access_token');

      if (accessToken != null && accessToken.isNotEmpty) {
        log('🔄 Environment.getHeaders() using SharedPreferences fallback');
        return {
          'Authorization': 'Bearer $accessToken',
          'Content-Type': 'application/json',
        };
      }
    } catch (e) {
      log('⚠️ SharedPreferences fallback failed: $e');
    }

    log('❌ All token retrieval methods failed');
    throw Exception('Access token not found. Please log in again.');
  }

  /// Fetch environment-specific URL by key
  static String _getEnvUrl(String keyPrefix) {
    final env = dotenv.env['ENV'];
    if (env == null) {
      throw Exception('Environment (ENV) not set.');
    }

    final urlKey = '${env.toUpperCase()}_$keyPrefix';
    final url = dotenv.env[urlKey];

    if (url == null) {
      throw Exception('Environment variable $urlKey not found.');
    }

    return url;
  }

  /// Get Gate Base URL
  static String get gateBaseUrl => _getEnvUrl('GATE_BASE_URL');

  /// Get Auth URL
  static String get authUrl => _getEnvUrl('AUTH_URL');

  /// Get Society URL
  static String get societyUrl => _getEnvUrl('SOCIETY_URL');

  /// Get Media URL
  static String get mediaUrl => _getEnvUrl('MEDIA_URL');
}
