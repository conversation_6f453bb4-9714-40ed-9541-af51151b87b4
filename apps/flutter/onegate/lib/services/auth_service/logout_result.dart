/// Result class for logout operations
class LogoutResult {
  bool success = false;
  String? error;
  List<String> issues = [];
  Map<String, dynamic> details = {};

  LogoutResult({
    this.success = false,
    this.error,
    List<String>? issues,
    Map<String, dynamic>? details,
  }) {
    this.issues = issues ?? [];
    this.details = details ?? {};
  }

  /// Add an issue to the result
  void addIssue(String issue) {
    issues.add(issue);
  }

  /// Get all issues as a formatted string
  String getIssues() {
    if (issues.isEmpty) return 'No issues';
    return issues.join(', ');
  }

  /// Check if there are any issues
  bool get hasIssues => issues.isNotEmpty;

  /// Add detail information
  void addDetail(String key, dynamic value) {
    details[key] = value;
  }

  @override
  String toString() {
    return 'LogoutResult(success: $success, error: $error, issues: ${issues.length}, details: ${details.length})';
  }
}
