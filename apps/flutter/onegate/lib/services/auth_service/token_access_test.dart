import 'dart:developer';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:get_it/get_it.dart';

import 'auth_service.dart';
import 'enhanced_token_refresh_manager.dart';
import '../../data/datasources/gate_storage.dart';
import '../../common/environment.dart';

/// Test service to verify token accessibility after login
class TokenAccessTest {
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();

  /// Comprehensive token access test
  static Future<Map<String, dynamic>> runTokenAccessTest() async {
    try {
      log("🧪 Starting comprehensive token access test");
      
      final results = <String, dynamic>{
        'timestamp': DateTime.now().toIso8601String(),
        'tests': <String, dynamic>{},
        'summary': <String, dynamic>{},
      };

      // Test 1: Secure Storage Access
      final secureTest = await _testSecureStorage();
      results['tests']['secure_storage'] = secureTest;

      // Test 2: Gate Storage Access
      final gateTest = await _testGateStorage();
      results['tests']['gate_storage'] = gateTest;

      // Test 3: SharedPreferences Access
      final prefsTest = await _testSharedPreferences();
      results['tests']['shared_preferences'] = prefsTest;

      // Test 4: Auth Service Access
      final authTest = await _testAuthService();
      results['tests']['auth_service'] = authTest;

      // Test 5: Token Refresh Manager Access
      final managerTest = await _testTokenRefreshManager();
      results['tests']['token_refresh_manager'] = managerTest;

      // Test 6: Environment Headers Access
      final envTest = await _testEnvironmentHeaders();
      results['tests']['environment_headers'] = envTest;

      // Calculate summary
      final allTests = results['tests'] as Map<String, dynamic>;
      final passedTests = allTests.values.where((test) => test['success'] == true).length;
      final totalTests = allTests.length;

      results['summary'] = {
        'total_tests': totalTests,
        'passed_tests': passedTests,
        'failed_tests': totalTests - passedTests,
        'success_rate': (passedTests / totalTests * 100).toStringAsFixed(1),
        'overall_success': passedTests == totalTests,
      };

      log("🧪 Token access test completed: $passedTests/$totalTests tests passed");
      return results;
    } catch (e) {
      log("❌ Error running token access test: $e");
      return {
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Test secure storage access
  static Future<Map<String, dynamic>> _testSecureStorage() async {
    try {
      final accessToken = await _secureStorage.read(key: 'access_token_secure');
      final refreshToken = await _secureStorage.read(key: 'refresh_token_secure');
      
      return {
        'success': accessToken != null,
        'access_token_present': accessToken != null,
        'refresh_token_present': refreshToken != null,
        'access_token_length': accessToken?.length ?? 0,
        'message': accessToken != null ? 'Tokens found in secure storage' : 'No tokens in secure storage',
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'message': 'Failed to access secure storage',
      };
    }
  }

  /// Test gate storage access
  static Future<Map<String, dynamic>> _testGateStorage() async {
    try {
      final gateStorage = GetIt.I<GateStorage>();
      final accessToken = await gateStorage.getAccessToken();
      final refreshToken = await gateStorage.getRefreshToken();
      
      return {
        'success': accessToken != null,
        'access_token_present': accessToken != null,
        'refresh_token_present': refreshToken != null,
        'access_token_length': accessToken?.length ?? 0,
        'message': accessToken != null ? 'Tokens found in gate storage' : 'No tokens in gate storage',
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'message': 'Failed to access gate storage',
      };
    }
  }

  /// Test SharedPreferences access
  static Future<Map<String, dynamic>> _testSharedPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final accessToken = prefs.getString('access_token');
      
      return {
        'success': accessToken != null,
        'access_token_present': accessToken != null,
        'access_token_length': accessToken?.length ?? 0,
        'message': accessToken != null ? 'Token found in SharedPreferences' : 'No token in SharedPreferences',
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'message': 'Failed to access SharedPreferences',
      };
    }
  }

  /// Test auth service access
  static Future<Map<String, dynamic>> _testAuthService() async {
    try {
      final authService = GetIt.I<AuthService>();
      final accessToken = await authService.getValidAccessToken();
      
      return {
        'success': accessToken != null,
        'access_token_present': accessToken != null,
        'access_token_length': accessToken?.length ?? 0,
        'message': accessToken != null ? 'Token retrieved from auth service' : 'No token from auth service',
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'message': 'Failed to access auth service',
      };
    }
  }

  /// Test token refresh manager access
  static Future<Map<String, dynamic>> _testTokenRefreshManager() async {
    try {
      final authService = GetIt.I<AuthService>();
      final accessToken = await authService.tokenRefreshManager.getValidAccessToken();
      
      return {
        'success': accessToken != null,
        'access_token_present': accessToken != null,
        'access_token_length': accessToken?.length ?? 0,
        'message': accessToken != null ? 'Token retrieved from refresh manager' : 'No token from refresh manager',
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'message': 'Failed to access token refresh manager',
      };
    }
  }

  /// Test environment headers access
  static Future<Map<String, dynamic>> _testEnvironmentHeaders() async {
    try {
      final headers = await Environment.getHeaders();
      final hasAuth = headers.containsKey('Authorization');
      final authHeader = headers['Authorization'];
      
      return {
        'success': hasAuth,
        'authorization_header_present': hasAuth,
        'authorization_header_length': authHeader?.length ?? 0,
        'message': hasAuth ? 'Authorization header available' : 'No authorization header',
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'message': 'Failed to get environment headers',
      };
    }
  }

  /// Quick token availability check
  static Future<bool> quickTokenCheck() async {
    try {
      // Try the most reliable method first
      final headers = await Environment.getHeaders();
      return headers.containsKey('Authorization');
    } catch (e) {
      log("❌ Quick token check failed: $e");
      return false;
    }
  }

  /// Log test results in a readable format
  static void logTestResults(Map<String, dynamic> results) {
    log("🧪 ===== TOKEN ACCESS TEST RESULTS =====");
    
    if (results.containsKey('error')) {
      log("❌ Test failed with error: ${results['error']}");
      return;
    }

    final summary = results['summary'] as Map<String, dynamic>;
    log("📊 Summary: ${summary['passed_tests']}/${summary['total_tests']} tests passed (${summary['success_rate']}%)");
    
    final tests = results['tests'] as Map<String, dynamic>;
    tests.forEach((testName, testResult) {
      final success = testResult['success'] as bool;
      final message = testResult['message'] as String;
      final icon = success ? '✅' : '❌';
      log("$icon $testName: $message");
    });
    
    log("🧪 =====================================");
  }
}
