import 'dart:async';
import 'dart:developer';
import 'package:flutter_appauth/flutter_appauth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_onegate/services/auth_service/models/auth_tokens.dart';
import 'package:flutter_onegate/services/auth_service/enhanced_auth_storage.dart';
import 'package:flutter_onegate/services/auth_service/dynamic_token_scheduler.dart';
import 'package:flutter_onegate/services/auth_service/enhanced_token_refresh_manager.dart';
import 'package:flutter_onegate/services/auth_service/jwt_token_utility.dart';
import 'package:flutter_onegate/services/auth_service/app_auth_config_manager.dart';

/// Enhanced auth state that includes server TTL information
class EnhancedAuthState {
  final AuthTokens? tokens;
  final bool isLoading;
  final String? error;
  final bool isAuthenticated;
  final DateTime? lastRefresh;
  final Duration? nextRefreshIn;

  const EnhancedAuthState({
    this.tokens,
    this.isLoading = false,
    this.error,
    this.isAuthenticated = false,
    this.lastRefresh,
    this.nextRefreshIn,
  });

  EnhancedAuthState copyWith({
    AuthTokens? tokens,
    bool? isLoading,
    String? error,
    bool? isAuthenticated,
    DateTime? lastRefresh,
    Duration? nextRefreshIn,
  }) {
    return EnhancedAuthState(
      tokens: tokens ?? this.tokens,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      lastRefresh: lastRefresh ?? this.lastRefresh,
      nextRefreshIn: nextRefreshIn ?? this.nextRefreshIn,
    );
  }

  @override
  String toString() {
    return 'EnhancedAuthState('
        'isAuthenticated: $isAuthenticated, '
        'isLoading: $isLoading, '
        'error: $error, '
        'nextRefreshIn: ${nextRefreshIn?.inMinutes}m'
        ')';
  }
}

/// Enhanced AuthController that integrates with existing DynamicTokenScheduler
/// Maintains compatibility with existing authentication patterns
class EnhancedAuthController extends StateNotifier<EnhancedAuthState> {
  final EnhancedAuthStorage _storage;
  final DynamicTokenScheduler _dynamicTokenScheduler;
  final EnhancedTokenRefreshManager _tokenRefreshManager;
  final FlutterAppAuth _appAuth;
  
  Timer? _refreshTimer;
  StreamSubscription<void>? _refreshSubscription;
  
  EnhancedAuthController({
    required EnhancedAuthStorage storage,
    required DynamicTokenScheduler dynamicTokenScheduler,
    required EnhancedTokenRefreshManager tokenRefreshManager,
    FlutterAppAuth? appAuth,
  }) : _storage = storage,
       _dynamicTokenScheduler = dynamicTokenScheduler,
       _tokenRefreshManager = tokenRefreshManager,
       _appAuth = appAuth ?? const FlutterAppAuth(),
       super(const EnhancedAuthState());

  /// Initialize controller and boot from storage
  Future<void> initialize() async {
    try {
      log('🚀 Initializing Enhanced Auth Controller');
      
      // Migrate legacy tokens if needed
      await _storage.migrateLegacyTokens();
      
      // Boot from storage
      await _bootFromStorage();
      
      // Initialize dynamic token scheduler with existing token refresh manager
      await _dynamicTokenScheduler.initialize(_tokenRefreshManager);
      
      log('✅ Enhanced Auth Controller initialized');
    } catch (e) {
      log('❌ Error initializing Enhanced Auth Controller: $e');
      state = state.copyWith(error: 'Initialization failed: $e');
    }
  }

  /// Boot authentication state from storage
  Future<void> _bootFromStorage() async {
    try {
      state = state.copyWith(isLoading: true);
      
      final tokens = await _storage.read();
      if (tokens == null) {
        log('ℹ️ No tokens found in storage');
        state = state.copyWith(
          isLoading: false,
          isAuthenticated: false,
        );
        return;
      }
      
      // Check token validity
      if (tokens.isRefreshTokenExpired) {
        log('⚠️ Refresh token expired, clearing storage');
        await _storage.clear();
        state = state.copyWith(
          isLoading: false,
          isAuthenticated: false,
        );
        return;
      }
      
      // Tokens are valid, restore authentication state
      log('✅ Valid tokens found, restoring authentication state');
      state = state.copyWith(
        tokens: tokens,
        isLoading: false,
        isAuthenticated: true,
        lastRefresh: DateTime.now(),
      );
      
      // Schedule automatic refresh using server TTLs
      await _scheduleAutoRefreshWithServerTTLs(tokens);
      
      // Start dynamic token scheduling
      await _dynamicTokenScheduler.startDynamicScheduling();
      
    } catch (e) {
      log('❌ Error booting from storage: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to restore session: $e',
      );
    }
  }

  /// Enhanced auto-refresh scheduling using server-supplied TTLs
  /// Integrates with existing DynamicTokenScheduler
  Future<void> _scheduleAutoRefreshWithServerTTLs(AuthTokens tokens) async {
    try {
      // Cancel existing timer
      _refreshTimer?.cancel();
      
      final now = DateTime.now();
      
      // Use server-supplied TTLs with existing dynamic buffer calculation
      final dynamicBuffer = tokens.dynamicAccessBuffer;
      final accessLead = tokens.accessExpiresAt.difference(now) - dynamicBuffer;
      final refreshLead = tokens.refreshExpiresAt.difference(now) - const Duration(minutes: 4);
      
      // Choose whichever comes FIRST (access refresh OR refresh token refresh)
      final when = accessLead < refreshLead ? accessLead : refreshLead;
      
      if (when.isNegative || when.inSeconds <= 0) {
        // If when is negative → call refreshToken() immediately
        log('⚠️ Token refresh needed immediately (when: ${when.inSeconds}s)');
        Timer(const Duration(milliseconds: 100), () => refreshToken());
        return;
      }
      
      log('⏰ Scheduling auto-refresh in ${when.inMinutes}m ${when.inSeconds % 60}s '
          '(access: ${accessLead.inMinutes}m, refresh: ${refreshLead.inMinutes}m)');
      
      // Update state with next refresh timing
      state = state.copyWith(nextRefreshIn: when);
      
      // Schedule refresh using Timer (integrates with existing patterns)
      _refreshTimer = Timer(when, () {
        log('⏰ Auto-refresh triggered by server TTL schedule');
        refreshToken();
      });
      
      // Also integrate with DynamicTokenScheduler for additional monitoring
      await _dynamicTokenScheduler.forceTokenAnalysis();
      
    } catch (e) {
      log('❌ Error scheduling auto-refresh: $e');
    }
  }

  /// Login with enhanced token handling
  Future<bool> login() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      log('🔐 Starting enhanced login flow');
      
      // Use existing AppAuthConfigManager for configuration
      final result = await _appAuth.authorizeAndExchangeCode(
        AuthorizationTokenRequest(
          AppAuthConfigManager.clientId,
          AppAuthConfigManager.redirectUrl,
          serviceConfiguration: AppAuthConfigManager.getServiceConfiguration(),
          scopes: AppAuthConfigManager.scopes,
          clientSecret: AppAuthConfigManager.clientSecret,
          additionalParameters: {
            'access_type': 'offline',
          },
        ),
      );
      
      if (result.accessToken == null || result.refreshToken == null) {
        throw Exception('Login failed - missing required tokens');
      }
      
      // Create AuthTokens with server TTLs
      final tokens = result.toAuthTokens();
      
      // Store tokens
      await _storage.write(tokens);
      
      // Update state
      state = state.copyWith(
        tokens: tokens,
        isLoading: false,
        isAuthenticated: true,
        lastRefresh: DateTime.now(),
      );
      
      // Schedule auto-refresh with server TTLs
      await _scheduleAutoRefreshWithServerTTLs(tokens);
      
      // Start dynamic token scheduling
      await _dynamicTokenScheduler.startDynamicScheduling();
      
      log('✅ Enhanced login completed successfully');
      return true;
      
    } catch (e) {
      log('❌ Enhanced login failed: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Login failed: $e',
        isAuthenticated: false,
      );
      return false;
    }
  }

  /// Enhanced token refresh using server TTLs
  Future<bool> refreshToken() async {
    try {
      final currentTokens = state.tokens;
      if (currentTokens == null) {
        log('❌ No tokens available for refresh');
        return false;
      }
      
      if (currentTokens.isRefreshTokenExpired) {
        log('❌ Refresh token expired, forcing logout');
        await logout();
        return false;
      }
      
      log('🔄 Starting enhanced token refresh');
      
      // Use existing AppAuthConfigManager for refresh request
      final result = await _appAuth.token(
        AppAuthConfigManager.getRefreshTokenRequest(currentTokens.refresh),
      );
      
      if (result.accessToken == null) {
        throw Exception('Token refresh failed - no access token received');
      }
      
      // Create updated tokens with server TTLs
      final updatedTokens = result.toAuthTokens();
      
      // Store updated tokens
      await _storage.write(updatedTokens);
      
      // Update state
      state = state.copyWith(
        tokens: updatedTokens,
        lastRefresh: DateTime.now(),
      );
      
      // Reschedule auto-refresh with new server TTLs
      await _scheduleAutoRefreshWithServerTTLs(updatedTokens);
      
      log('✅ Enhanced token refresh completed');
      return true;
      
    } catch (e) {
      log('❌ Enhanced token refresh failed: $e');
      
      // Check if it's a permanent failure (invalid_grant, invalid_token)
      if (e.toString().contains('invalid_grant') || 
          e.toString().contains('invalid_token')) {
        log('🚪 Permanent refresh failure, forcing logout');
        await logout();
      }
      
      return false;
    }
  }

  /// Enhanced logout with comprehensive cleanup
  Future<void> logout() async {
    try {
      log('🚪 Starting enhanced logout');
      
      // Cancel timers
      _refreshTimer?.cancel();
      _refreshSubscription?.cancel();
      
      // Stop dynamic token scheduling
      _dynamicTokenScheduler.stopDynamicScheduling();
      
      // Clear storage using existing patterns
      await _storage.clear();
      await _tokenRefreshManager.clearTokens();
      
      // Update state
      state = state.copyWith(
        tokens: null,
        isAuthenticated: false,
        error: null,
        lastRefresh: null,
        nextRefreshIn: null,
      );
      
      log('✅ Enhanced logout completed');
      
    } catch (e) {
      log('❌ Error during enhanced logout: $e');
    }
  }

  /// Get current access token (integrates with existing patterns)
  String? get accessToken => state.tokens?.access;
  
  /// Get current refresh token
  String? get refreshToken => state.tokens?.refresh;
  
  /// Check if authenticated
  bool get isAuthenticated => state.isAuthenticated;
  
  /// Get time until next refresh
  Duration? get timeUntilNextRefresh => state.nextRefreshIn;
  
  /// Force immediate token refresh
  Future<bool> forceRefresh() async {
    log('🔄 Forcing immediate token refresh');
    return await refreshToken();
  }
  
  /// Get comprehensive auth status for debugging
  Map<String, dynamic> getAuthStatus() {
    final tokens = state.tokens;
    return {
      'isAuthenticated': state.isAuthenticated,
      'isLoading': state.isLoading,
      'error': state.error,
      'hasTokens': tokens != null,
      'accessTokenExpiring': tokens?.isAccessTokenExpiring,
      'refreshTokenExpired': tokens?.isRefreshTokenExpired,
      'timeUntilAccessExpiry': tokens?.timeUntilAccessExpiry.inMinutes,
      'timeUntilRefreshExpiry': tokens?.timeUntilRefreshExpiry.inMinutes,
      'nextRefreshIn': state.nextRefreshIn?.inMinutes,
      'lastRefresh': state.lastRefresh?.toIso8601String(),
      'dynamicBuffer': tokens?.dynamicAccessBuffer.inMinutes,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    _refreshSubscription?.cancel();
    _dynamicTokenScheduler.dispose();
    super.dispose();
  }
}

/// Provider for Enhanced Auth Controller
final enhancedAuthControllerProvider = 
    StateNotifierProvider<EnhancedAuthController, EnhancedAuthState>((ref) {
  // Dependencies would be injected here based on your existing DI setup
  throw UnimplementedError('Provider should be overridden with actual dependencies');
});

/// Convenience providers for common auth state
final isAuthenticatedProvider = Provider<bool>((ref) {
  return ref.watch(enhancedAuthControllerProvider).isAuthenticated;
});

final accessTokenProvider = Provider<String?>((ref) {
  return ref.watch(enhancedAuthControllerProvider).tokens?.access;
});

final authLoadingProvider = Provider<bool>((ref) {
  return ref.watch(enhancedAuthControllerProvider).isLoading;
});
