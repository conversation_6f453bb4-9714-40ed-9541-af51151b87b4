import 'package:flutter_onegate/services/auth_service/jwt_token_utility.dart';

/// Enhanced AuthTokens model with server-supplied TTL integration
/// Integrates with existing JwtTokenUtility for dynamic token analysis
class AuthTokens {
  final String access;
  final String refresh;
  final String? id;
  final DateTime accessExpiresAt;    // calculated from server expires_in
  final DateTime refreshExpiresAt;   // calculated from server refresh_expires_in
  final DateTime issuedAt;           // when tokens were issued
  
  const AuthTokens({
    required this.access,
    required this.refresh,
    this.id,
    required this.accessExpiresAt,
    required this.refreshExpiresAt,
    required this.issuedAt,
  });

  /// Create AuthTokens from server token response with dynamic TTL calculation
  factory AuthTokens.fromTokenResponse({
    required String accessToken,
    required String refreshToken,
    String? idToken,
    required int expiresInSeconds,        // server-supplied expires_in
    required int refreshExpiresInSeconds, // server-supplied refresh_expires_in
    DateTime? issuedAt,
  }) {
    final now = issuedAt ?? DateTime.now();
    
    return AuthTokens(
      access: accessToken,
      refresh: refreshToken,
      id: idToken,
      accessExpiresAt: now.add(Duration(seconds: expiresInSeconds)),
      refreshExpiresAt: now.add(Duration(seconds: refreshExpiresInSeconds)),
      issuedAt: now,
    );
  }

  /// Create AuthTokens from JWT tokens with automatic TTL extraction
  /// Integrates with existing JwtTokenUtility for backward compatibility
  factory AuthTokens.fromJwtTokens({
    required String accessToken,
    required String refreshToken,
    String? idToken,
    DateTime? issuedAt,
  }) {
    final now = issuedAt ?? DateTime.now();
    
    // Use existing JwtTokenUtility to extract expiration times
    final accessExpiry = JwtTokenUtility.getTokenExpirationTime(accessToken);
    final refreshExpiry = JwtTokenUtility.getTokenExpirationTime(refreshToken);
    
    return AuthTokens(
      access: accessToken,
      refresh: refreshToken,
      id: idToken,
      accessExpiresAt: accessExpiry ?? now.add(const Duration(hours: 1)), // fallback
      refreshExpiresAt: refreshExpiry ?? now.add(const Duration(days: 1)), // fallback
      issuedAt: now,
    );
  }

  /// Integration with existing JwtTokenUtility - get dynamic access buffer
  Duration get dynamicAccessBuffer {
    try {
      return JwtTokenUtility.calculateOptimalRefreshBuffer(access);
    } catch (e) {
      // Fallback to 2-minute buffer if JWT analysis fails
      return const Duration(minutes: 2);
    }
  }

  /// Integration with existing JwtTokenUtility - get optimal refresh time
  DateTime get optimalRefreshTime {
    try {
      final jwtOptimalTime = JwtTokenUtility.getOptimalRefreshTime(access);
      if (jwtOptimalTime != null) {
        return jwtOptimalTime;
      }
    } catch (e) {
      // Continue to fallback calculation
    }
    
    // Fallback: use server TTL with dynamic buffer
    return accessExpiresAt.subtract(dynamicAccessBuffer);
  }

  /// Get time until access token expires
  Duration get timeUntilAccessExpiry {
    final now = DateTime.now();
    final remaining = accessExpiresAt.difference(now);
    return remaining.isNegative ? Duration.zero : remaining;
  }

  /// Get time until refresh token expires
  Duration get timeUntilRefreshExpiry {
    final now = DateTime.now();
    final remaining = refreshExpiresAt.difference(now);
    return remaining.isNegative ? Duration.zero : remaining;
  }

  /// Check if access token is expired or expiring soon
  bool get isAccessTokenExpiring {
    return JwtTokenUtility.isTokenExpiredOrExpiring(access, buffer: dynamicAccessBuffer);
  }

  /// Check if refresh token is expired
  bool get isRefreshTokenExpired {
    return DateTime.now().isAfter(refreshExpiresAt);
  }

  /// Check if both tokens are valid
  bool get areTokensValid {
    return !isAccessTokenExpiring && !isRefreshTokenExpired;
  }

  /// Get comprehensive token analysis using existing JwtTokenUtility
  Map<String, dynamic> get tokenAnalysis {
    return JwtTokenUtility.getTokenAnalysis(access);
  }

  /// Calculate when to schedule next refresh (integrates with DynamicTokenScheduler)
  /// Returns the earlier of: access token refresh time OR refresh token refresh time
  DateTime get nextScheduledRefreshTime {
    final now = DateTime.now();
    
    // Access token refresh: optimal time calculated by JWT utility
    final accessRefreshTime = optimalRefreshTime;
    
    // Refresh token refresh: 4 minutes before refresh token expires
    final refreshRefreshTime = refreshExpiresAt.subtract(const Duration(minutes: 4));
    
    // Return whichever comes first, but not in the past
    final earliestTime = accessRefreshTime.isBefore(refreshRefreshTime) 
        ? accessRefreshTime 
        : refreshRefreshTime;
    
    return earliestTime.isBefore(now) ? now : earliestTime;
  }

  /// Calculate delay until next scheduled refresh
  Duration get delayUntilNextRefresh {
    final now = DateTime.now();
    final nextRefresh = nextScheduledRefreshTime;
    final delay = nextRefresh.difference(now);
    return delay.isNegative ? Duration.zero : delay;
  }

  /// Create updated tokens after refresh (preserves refresh token if not rotated)
  AuthTokens copyWithRefreshedTokens({
    required String newAccessToken,
    String? newRefreshToken,
    String? newIdToken,
    required int expiresInSeconds,
    int? refreshExpiresInSeconds,
  }) {
    final now = DateTime.now();
    
    return AuthTokens(
      access: newAccessToken,
      refresh: newRefreshToken ?? refresh, // keep existing if not rotated
      id: newIdToken ?? id,
      accessExpiresAt: now.add(Duration(seconds: expiresInSeconds)),
      refreshExpiresAt: refreshExpiresInSeconds != null 
          ? now.add(Duration(seconds: refreshExpiresInSeconds))
          : refreshExpiresAt, // keep existing expiry if not provided
      issuedAt: now,
    );
  }

  /// Convert to JSON for storage (integrates with existing storage patterns)
  Map<String, dynamic> toJson() {
    return {
      'access': access,
      'refresh': refresh,
      'id': id,
      'accessExpiresAt': accessExpiresAt.toIso8601String(),
      'refreshExpiresAt': refreshExpiresAt.toIso8601String(),
      'issuedAt': issuedAt.toIso8601String(),
    };
  }

  /// Create from JSON storage (integrates with existing storage patterns)
  factory AuthTokens.fromJson(Map<String, dynamic> json) {
    return AuthTokens(
      access: json['access'] as String,
      refresh: json['refresh'] as String,
      id: json['id'] as String?,
      accessExpiresAt: DateTime.parse(json['accessExpiresAt'] as String),
      refreshExpiresAt: DateTime.parse(json['refreshExpiresAt'] as String),
      issuedAt: DateTime.parse(json['issuedAt'] as String),
    );
  }

  /// Debug string representation
  @override
  String toString() {
    return 'AuthTokens('
        'accessExpiresAt: $accessExpiresAt, '
        'refreshExpiresAt: $refreshExpiresAt, '
        'timeUntilAccessExpiry: ${timeUntilAccessExpiry.inMinutes}m, '
        'timeUntilRefreshExpiry: ${timeUntilRefreshExpiry.inMinutes}m, '
        'dynamicBuffer: ${dynamicAccessBuffer.inMinutes}m, '
        'nextRefresh: $nextScheduledRefreshTime'
        ')';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthTokens &&
        other.access == access &&
        other.refresh == refresh &&
        other.id == id &&
        other.accessExpiresAt == accessExpiresAt &&
        other.refreshExpiresAt == refreshExpiresAt &&
        other.issuedAt == issuedAt;
  }

  @override
  int get hashCode {
    return Object.hash(
      access,
      refresh,
      id,
      accessExpiresAt,
      refreshExpiresAt,
      issuedAt,
    );
  }
}

/// Extension for flutter_appauth TokenResponse integration
extension TokenResponseExtension on dynamic {
  /// Convert flutter_appauth TokenResponse to AuthTokens with server TTLs
  AuthTokens toAuthTokens() {
    // Handle both flutter_appauth TokenResponse and Map<String, dynamic>
    final accessToken = this is Map 
        ? this['access_token'] as String?
        : this.accessToken as String?;
    final refreshToken = this is Map 
        ? this['refresh_token'] as String?
        : this.refreshToken as String?;
    final idToken = this is Map 
        ? this['id_token'] as String?
        : this.idToken as String?;
    final expiresIn = this is Map 
        ? this['expires_in'] as int?
        : this.accessTokenExpirationDateTime?.difference(DateTime.now()).inSeconds;
    final refreshExpiresIn = this is Map 
        ? this['refresh_expires_in'] as int?
        : null;

    if (accessToken == null || refreshToken == null) {
      throw ArgumentError('Invalid token response: missing required tokens');
    }

    // Use server-supplied TTLs if available, otherwise extract from JWT
    if (expiresIn != null && refreshExpiresIn != null) {
      return AuthTokens.fromTokenResponse(
        accessToken: accessToken,
        refreshToken: refreshToken,
        idToken: idToken,
        expiresInSeconds: expiresIn,
        refreshExpiresInSeconds: refreshExpiresIn,
      );
    } else {
      // Fallback to JWT extraction for backward compatibility
      return AuthTokens.fromJwtTokens(
        accessToken: accessToken,
        refreshToken: refreshToken,
        idToken: idToken,
      );
    }
  }
}
