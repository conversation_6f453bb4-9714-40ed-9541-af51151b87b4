import 'dart:convert';
import 'dart:developer';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter_onegate/services/auth_service/models/auth_tokens.dart';
import 'package:flutter_onegate/data/datasources/gate_storage.dart';

/// Enhanced auth storage that integrates with existing storage patterns
/// Maintains compatibility with existing GateStorage and secure storage implementations
class EnhancedAuthStorage {
  static const String _tokensKey = 'enhanced_auth_tokens';
  static const String _legacyAccessTokenKey = 'access_token';
  static const String _legacyRefreshTokenKey = 'refresh_token';
  static const String _legacyIdTokenKey = 'id_token';
  
  final FlutterSecureStorage _secureStorage;
  final GateStorage _gateStorage;
  
  EnhancedAuthStorage({
    FlutterSecureStorage? secureStorage,
    GateStorage? gateStorage,
  }) : _secureStorage = secureStorage ?? const FlutterSecureStorage(),
       _gateStorage = gateStorage ?? GateStorage();

  /// Read tokens from storage with backward compatibility
  Future<AuthTokens?> read() async {
    try {
      // Try to read enhanced tokens first
      final enhancedTokensJson = await _secureStorage.read(key: _tokensKey);
      if (enhancedTokensJson != null) {
        final tokensMap = jsonDecode(enhancedTokensJson) as Map<String, dynamic>;
        return AuthTokens.fromJson(tokensMap);
      }
      
      // Fallback to legacy storage for backward compatibility
      return await _readLegacyTokens();
    } catch (e) {
      log('❌ Error reading tokens from storage: $e');
      return null;
    }
  }

  /// Write tokens to storage (both enhanced and legacy for compatibility)
  Future<void> write(AuthTokens tokens) async {
    try {
      // Write enhanced tokens
      final tokensJson = jsonEncode(tokens.toJson());
      await _secureStorage.write(key: _tokensKey, value: tokensJson);
      
      // Write to legacy storage for backward compatibility
      await _writeLegacyTokens(tokens);
      
      log('✅ Enhanced auth tokens stored successfully');
    } catch (e) {
      log('❌ Error writing tokens to storage: $e');
      throw Exception('Failed to store auth tokens: $e');
    }
  }

  /// Clear all tokens from storage
  Future<void> clear() async {
    try {
      // Clear enhanced tokens
      await _secureStorage.delete(key: _tokensKey);
      
      // Clear legacy tokens for complete cleanup
      await _clearLegacyTokens();
      
      log('✅ All auth tokens cleared from storage');
    } catch (e) {
      log('❌ Error clearing tokens from storage: $e');
    }
  }

  /// Check if tokens exist in storage
  Future<bool> hasTokens() async {
    try {
      final enhancedTokens = await _secureStorage.read(key: _tokensKey);
      if (enhancedTokens != null) return true;
      
      // Check legacy storage
      final legacyAccessToken = await _secureStorage.read(key: _legacyAccessTokenKey);
      return legacyAccessToken != null;
    } catch (e) {
      log('❌ Error checking token existence: $e');
      return false;
    }
  }

  /// Get access token directly (for quick access)
  Future<String?> getAccessToken() async {
    try {
      final tokens = await read();
      return tokens?.access;
    } catch (e) {
      log('❌ Error getting access token: $e');
      return null;
    }
  }

  /// Get refresh token directly (for quick access)
  Future<String?> getRefreshToken() async {
    try {
      final tokens = await read();
      return tokens?.refresh;
    } catch (e) {
      log('❌ Error getting refresh token: $e');
      return null;
    }
  }

  /// Check if access token is expired or expiring
  Future<bool> isAccessTokenExpiring() async {
    try {
      final tokens = await read();
      return tokens?.isAccessTokenExpiring ?? true;
    } catch (e) {
      log('❌ Error checking access token expiration: $e');
      return true;
    }
  }

  /// Check if refresh token is expired
  Future<bool> isRefreshTokenExpired() async {
    try {
      final tokens = await read();
      return tokens?.isRefreshTokenExpired ?? true;
    } catch (e) {
      log('❌ Error checking refresh token expiration: $e');
      return true;
    }
  }

  /// Get time until next scheduled refresh
  Future<Duration?> getTimeUntilNextRefresh() async {
    try {
      final tokens = await read();
      return tokens?.delayUntilNextRefresh;
    } catch (e) {
      log('❌ Error getting time until next refresh: $e');
      return null;
    }
  }

  /// Migrate legacy tokens to enhanced format
  Future<void> migrateLegacyTokens() async {
    try {
      // Check if enhanced tokens already exist
      final enhancedTokens = await _secureStorage.read(key: _tokensKey);
      if (enhancedTokens != null) {
        log('ℹ️ Enhanced tokens already exist, skipping migration');
        return;
      }
      
      // Read legacy tokens
      final legacyTokens = await _readLegacyTokens();
      if (legacyTokens != null) {
        log('🔄 Migrating legacy tokens to enhanced format');
        await write(legacyTokens);
        log('✅ Legacy token migration completed');
      }
    } catch (e) {
      log('❌ Error migrating legacy tokens: $e');
    }
  }

  /// Read tokens from legacy storage (backward compatibility)
  Future<AuthTokens?> _readLegacyTokens() async {
    try {
      final accessToken = await _secureStorage.read(key: _legacyAccessTokenKey);
      final refreshToken = await _secureStorage.read(key: _legacyRefreshTokenKey);
      final idToken = await _secureStorage.read(key: _legacyIdTokenKey);
      
      if (accessToken == null || refreshToken == null) {
        return null;
      }
      
      // Create AuthTokens from JWT tokens (extracts expiration from JWT)
      return AuthTokens.fromJwtTokens(
        accessToken: accessToken,
        refreshToken: refreshToken,
        idToken: idToken,
      );
    } catch (e) {
      log('❌ Error reading legacy tokens: $e');
      return null;
    }
  }

  /// Write tokens to legacy storage (backward compatibility)
  Future<void> _writeLegacyTokens(AuthTokens tokens) async {
    try {
      await _secureStorage.write(key: _legacyAccessTokenKey, value: tokens.access);
      await _secureStorage.write(key: _legacyRefreshTokenKey, value: tokens.refresh);
      if (tokens.id != null) {
        await _secureStorage.write(key: _legacyIdTokenKey, value: tokens.id!);
      }
      
      // Also write to GateStorage for existing compatibility
      await _gateStorage.saveAccessToken(tokens.access);
      await _gateStorage.saveRefreshToken(tokens.refresh);
      await _gateStorage.saveTokenExpiry(tokens.accessExpiresAt);
      
      log('✅ Legacy tokens updated for backward compatibility');
    } catch (e) {
      log('❌ Error writing legacy tokens: $e');
    }
  }

  /// Clear tokens from legacy storage
  Future<void> _clearLegacyTokens() async {
    try {
      await _secureStorage.delete(key: _legacyAccessTokenKey);
      await _secureStorage.delete(key: _legacyRefreshTokenKey);
      await _secureStorage.delete(key: _legacyIdTokenKey);
      
      // Clear from GateStorage
      await _gateStorage.clearTokens();
      
      log('✅ Legacy tokens cleared');
    } catch (e) {
      log('❌ Error clearing legacy tokens: $e');
    }
  }

  /// Get storage statistics for debugging
  Future<Map<String, dynamic>> getStorageStats() async {
    try {
      final tokens = await read();
      final hasEnhanced = await _secureStorage.read(key: _tokensKey) != null;
      final hasLegacyAccess = await _secureStorage.read(key: _legacyAccessTokenKey) != null;
      
      return {
        'hasEnhancedTokens': hasEnhanced,
        'hasLegacyTokens': hasLegacyAccess,
        'tokensValid': tokens?.areTokensValid ?? false,
        'accessTokenExpiring': tokens?.isAccessTokenExpiring ?? true,
        'refreshTokenExpired': tokens?.isRefreshTokenExpired ?? true,
        'timeUntilAccessExpiry': tokens?.timeUntilAccessExpiry.inMinutes,
        'timeUntilRefreshExpiry': tokens?.timeUntilRefreshExpiry.inMinutes,
        'nextRefreshIn': tokens?.delayUntilNextRefresh.inMinutes,
        'dynamicBuffer': tokens?.dynamicAccessBuffer.inMinutes,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Log comprehensive storage status for debugging
  Future<void> logStorageStatus() async {
    try {
      final stats = await getStorageStats();
      log('📊 ===== ENHANCED AUTH STORAGE STATUS =====');
      log('🔧 Has Enhanced Tokens: ${stats['hasEnhancedTokens']}');
      log('🔧 Has Legacy Tokens: ${stats['hasLegacyTokens']}');
      log('✅ Tokens Valid: ${stats['tokensValid']}');
      log('⏰ Access Token Expiring: ${stats['accessTokenExpiring']}');
      log('⏰ Refresh Token Expired: ${stats['refreshTokenExpired']}');
      log('⏱️ Time Until Access Expiry: ${stats['timeUntilAccessExpiry']} minutes');
      log('⏱️ Time Until Refresh Expiry: ${stats['timeUntilRefreshExpiry']} minutes');
      log('🔄 Next Refresh In: ${stats['nextRefreshIn']} minutes');
      log('🔄 Dynamic Buffer: ${stats['dynamicBuffer']} minutes');
      log('📊 ==========================================');
    } catch (e) {
      log('❌ Error logging storage status: $e');
    }
  }
}
