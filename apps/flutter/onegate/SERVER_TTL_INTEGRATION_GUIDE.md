# 🔄 Server TTL Integration Guide

## Overview

This guide shows how to integrate the new server TTL-aware authentication system with your existing OneGate dynamic token management implementation. The enhanced system uses server-supplied `expires_in` and `refresh_expires_in` values instead of hardcoded timeouts while maintaining full backward compatibility.

## 🎯 Key Integration Points

### 1. **Enhanced AuthTokens Model**
**File**: `lib/services/auth_service/models/auth_tokens.dart`

**Integration**: Replaces hardcoded token expiration with server-supplied TTLs while integrating with existing `JwtTokenUtility`.

```dart
// Before: Manual expiration calculation
final expiryTime = DateTime.now().add(Duration(minutes: 9, seconds: 48));

// After: Server TTL integration
final tokens = AuthTokens.fromTokenResponse(
  accessToken: result.accessToken!,
  refreshToken: result.refreshToken!,
  expiresInSeconds: result.expiresIn!, // From server
  refreshExpiresInSeconds: result.refreshExpiresIn!, // From server
);

// Integrates with existing JWT utilities
final dynamicBuffer = tokens.dynamicAccessBuffer; // Uses JwtTokenUtility
final optimalTime = tokens.optimalRefreshTime;    // Uses JwtTokenUtility
```

### 2. **Enhanced Auth Storage**
**File**: `lib/services/auth_service/enhanced_auth_storage.dart`

**Integration**: Extends existing storage patterns with server TTL support and backward compatibility.

```dart
// Maintains compatibility with existing GateStorage
final storage = EnhancedAuthStorage();

// Automatic migration from legacy storage
await storage.migrateLegacyTokens();

// Server TTL-aware validation
final isExpiring = await storage.isAccessTokenExpiring(); // Uses server TTLs
final timeUntilRefresh = await storage.getTimeUntilNextRefresh();
```

### 3. **Enhanced Riverpod AuthController**
**File**: `lib/services/auth_service/riverpod/enhanced_auth_controller.dart`

**Integration**: Integrates with existing `DynamicTokenScheduler` and `EnhancedTokenRefreshManager`.

```dart
// Enhanced scheduling using server TTLs + existing dynamic buffer
Future<void> _scheduleAutoRefreshWithServerTTLs(AuthTokens tokens) async {
  final now = DateTime.now();
  
  // Use server TTLs with existing dynamic buffer calculation
  final dynamicBuffer = tokens.dynamicAccessBuffer; // JwtTokenUtility integration
  final accessLead = tokens.accessExpiresAt.difference(now) - dynamicBuffer;
  final refreshLead = tokens.refreshExpiresAt.difference(now) - Duration(minutes: 4);
  
  final when = accessLead < refreshLead ? accessLead : refreshLead;
  
  // Integrate with existing DynamicTokenScheduler
  await _dynamicTokenScheduler.forceTokenAnalysis();
}
```

### 4. **Enhanced Auth Interceptor**
**File**: `lib/services/auth_service/enhanced_auth_interceptor.dart` (updated)

**Integration**: Updates existing interceptor to use server TTL-aware token validation.

```dart
// Server TTL-aware token validation
final tokens = await _enhancedStorage.read();

if (tokens != null && !tokens.isAccessTokenExpiring) {
  // Use existing valid token (server TTL validation)
  accessToken = tokens.access;
  log("🔑 Using valid access token (expires in ${tokens.timeUntilAccessExpiry.inMinutes}m)");
} else {
  // Get refreshed token via existing dynamic auth
  accessToken = await _dynamicAuth.getEnhancedAccessToken();
}
```

## 🔧 Migration Steps

### Step 1: Update Token Response Handling

**Current Implementation**:
```dart
// In your existing auth service
final result = await _appAuth.authorizeAndExchangeCode(request);
await _storeTokens(result); // Hardcoded expiration
```

**Enhanced Implementation**:
```dart
// Enhanced with server TTL support
final result = await _appAuth.authorizeAndExchangeCode(request);
final tokens = result.toAuthTokens(); // Extracts server TTLs
await _enhancedStorage.write(tokens);
```

### Step 2: Update Dependency Injection

**Add to your DI setup**:
```dart
// Register enhanced components
GetIt.I.registerSingleton<EnhancedAuthStorage>(EnhancedAuthStorage());

// Update existing auth controller provider
final enhancedAuthControllerProvider = 
    StateNotifierProvider<EnhancedAuthController, EnhancedAuthState>((ref) {
  return EnhancedAuthController(
    storage: GetIt.I<EnhancedAuthStorage>(),
    dynamicTokenScheduler: GetIt.I<DynamicTokenScheduler>(), // Existing
    tokenRefreshManager: GetIt.I<EnhancedTokenRefreshManager>(), // Existing
  );
});
```

### Step 3: Update Existing Interceptors

**Replace existing auth interceptor**:
```dart
// In your Dio setup
_dio.interceptors.add(
  EnhancedAuthInterceptor(
    tokenManager: GetIt.I<EnhancedTokenRefreshManager>(), // Existing
  ),
);
```

### Step 4: Update UI Integration

**Riverpod integration**:
```dart
// Use enhanced providers
class AuthWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(enhancedAuthControllerProvider);
    final isAuthenticated = ref.watch(isAuthenticatedProvider);
    
    // Server TTL-aware UI updates
    if (authState.nextRefreshIn != null) {
      return Text('Next refresh in: ${authState.nextRefreshIn!.inMinutes}m');
    }
    
    return authState.isLoading 
        ? CircularProgressIndicator()
        : LoginButton();
  }
}
```

## 🔄 Backward Compatibility

### Automatic Migration
The enhanced system automatically migrates existing tokens:

```dart
// Automatic migration on first use
final storage = EnhancedAuthStorage();
await storage.migrateLegacyTokens(); // Converts existing tokens

// Maintains existing storage compatibility
await storage.write(tokens); // Updates both enhanced and legacy storage
```

### Existing API Compatibility
All existing authentication APIs continue to work:

```dart
// Existing code continues to work
final accessToken = await authService.getValidAccessToken();
final isAuthenticated = await authService.isAuthenticated();

// Enhanced features available when needed
final tokens = await enhancedStorage.read();
final timeUntilRefresh = tokens?.delayUntilNextRefresh;
```

## 🧪 Testing Integration

### Run Integration Tests
```bash
# Test server TTL parsing and scheduling
flutter test test/services/auth_service/server_ttl_integration_test.dart

# Test existing functionality still works
flutter test test/services/auth_service/
```

### Manual Testing Scenarios

1. **Server TTL Parsing**:
   - Login and verify tokens use server-supplied TTLs
   - Check logs for "Server TTL info" messages

2. **Dynamic Refresh Scheduling**:
   - Verify refresh happens before server-supplied expiration
   - Check integration with existing `DynamicTokenScheduler`

3. **401 Error Handling**:
   - Verify no force logout on access token expiry
   - Verify logout only on refresh token failure

4. **Backward Compatibility**:
   - Existing tokens migrate automatically
   - Existing APIs continue to work

## 📊 Monitoring and Debugging

### Enhanced Logging
```dart
// Comprehensive status logging
await enhancedStorage.logStorageStatus();
await dynamicAuth.logComprehensiveTokenDetails();

// Server TTL-specific logs
log("⏰ Server TTL info - Access expires in: ${tokens.timeUntilAccessExpiry.inMinutes}m");
log("🔄 Dynamic buffer: ${tokens.dynamicAccessBuffer.inMinutes}m");
```

### Debug UI (Development Only)
```dart
// Add to debug builds
if (kDebugMode) {
  FloatingActionButton(
    onPressed: () async {
      final storage = GetIt.I<EnhancedAuthStorage>();
      final stats = await storage.getStorageStats();
      showDialog(context: context, builder: (_) => 
        AlertDialog(content: Text(stats.toString())));
    },
    child: Icon(Icons.info),
  );
}
```

## 🚀 Benefits of Integration

1. **Server TTL Compliance**: Uses actual server-supplied token lifetimes
2. **Enhanced Reliability**: Never force logout on access token expiry
3. **Backward Compatibility**: Existing code continues to work
4. **Improved Monitoring**: Better visibility into token timing
5. **Dynamic Adaptation**: Automatically adapts to server TTL changes
6. **Existing Integration**: Works with current `DynamicTokenScheduler` and `JwtTokenUtility`

## 🔧 Configuration

### Server Response Format
Ensure your auth server returns TTL information:

```json
{
  "access_token": "eyJhbGciOi...",
  "expires_in": 120,           // Required: seconds until access token expires
  "refresh_token": "BJyZ...",
  "refresh_expires_in": 600,   // Required: seconds until refresh token expires
  "id_token": "eyJ0eXAiOi..."
}
```

### Fallback Behavior
If server doesn't provide TTLs, system falls back to JWT extraction:

```dart
// Automatic fallback to JWT parsing
final tokens = AuthTokens.fromJwtTokens(
  accessToken: result.accessToken!,
  refreshToken: result.refreshToken!,
  // Extracts expiration from JWT claims using existing JwtTokenUtility
);
```

This integration maintains full compatibility with your existing dynamic token system while adding server TTL awareness for improved reliability and compliance.
