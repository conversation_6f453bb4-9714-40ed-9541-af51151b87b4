import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:fake_async/fake_async.dart';
import 'package:flutter_onegate/services/auth_service/models/auth_tokens.dart';
import 'package:flutter_onegate/services/auth_service/enhanced_auth_storage.dart';
import 'package:flutter_onegate/services/auth_service/riverpod/enhanced_auth_controller.dart';
import 'package:flutter_onegate/services/auth_service/dynamic_token_scheduler.dart';
import 'package:flutter_onegate/services/auth_service/enhanced_token_refresh_manager.dart';
import 'package:flutter_onegate/services/auth_service/jwt_token_utility.dart';

/// Integration tests for server TTL-aware authentication system
/// Tests the integration between AuthTokens, EnhancedAuthStorage, and DynamicTokenScheduler
void main() {
  group('Server TTL Integration Tests', () {
    late EnhancedAuthStorage storage;
    late DynamicTokenScheduler scheduler;
    late EnhancedTokenRefreshManager tokenManager;

    setUp(() {
      storage = EnhancedAuthStorage();
      scheduler = DynamicTokenScheduler();
      // tokenManager would be mocked in real tests
    });

    group('AuthTokens Server TTL Parsing', () {
      test('should create AuthTokens from server response with TTLs', () {
        // Simulate server token response
        final serverResponse = {
          'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
          'refresh_token': 'refresh_token_value',
          'id_token': 'id_token_value',
          'expires_in': 120,  // 2 minutes
          'refresh_expires_in': 600,  // 10 minutes
        };

        final tokens = serverResponse.toAuthTokens();

        expect(tokens.access, equals(serverResponse['access_token']));
        expect(tokens.refresh, equals(serverResponse['refresh_token']));
        expect(tokens.id, equals(serverResponse['id_token']));
        
        // Verify TTL calculations
        final now = DateTime.now();
        expect(tokens.accessExpiresAt.isAfter(now), isTrue);
        expect(tokens.refreshExpiresAt.isAfter(now), isTrue);
        expect(tokens.accessExpiresAt.isBefore(tokens.refreshExpiresAt), isTrue);
        
        // Verify TTL durations (with some tolerance for test execution time)
        final accessDuration = tokens.accessExpiresAt.difference(tokens.issuedAt);
        final refreshDuration = tokens.refreshExpiresAt.difference(tokens.issuedAt);
        
        expect(accessDuration.inSeconds, closeTo(120, 5));
        expect(refreshDuration.inSeconds, closeTo(600, 5));
      });

      test('should integrate with existing JwtTokenUtility for dynamic buffers', () {
        final tokens = AuthTokens.fromTokenResponse(
          accessToken: _createMockJwtToken(expiresInMinutes: 10),
          refreshToken: 'refresh_token',
          expiresInSeconds: 600,  // 10 minutes
          refreshExpiresInSeconds: 3600,  // 1 hour
        );

        // Test integration with existing JWT utility
        final dynamicBuffer = tokens.dynamicAccessBuffer;
        expect(dynamicBuffer.inMinutes, greaterThan(0));
        expect(dynamicBuffer.inMinutes, lessThanOrEqualTo(5)); // Reasonable buffer

        final optimalRefreshTime = tokens.optimalRefreshTime;
        expect(optimalRefreshTime.isBefore(tokens.accessExpiresAt), isTrue);
        expect(optimalRefreshTime.isAfter(DateTime.now()), isTrue);
      });

      test('should calculate next scheduled refresh time correctly', () {
        final tokens = AuthTokens.fromTokenResponse(
          accessToken: _createMockJwtToken(expiresInMinutes: 10),
          refreshToken: 'refresh_token',
          expiresInSeconds: 600,  // 10 minutes
          refreshExpiresInSeconds: 3600,  // 1 hour
        );

        final nextRefresh = tokens.nextScheduledRefreshTime;
        final delay = tokens.delayUntilNextRefresh;

        expect(nextRefresh.isAfter(DateTime.now()), isTrue);
        expect(nextRefresh.isBefore(tokens.accessExpiresAt), isTrue);
        expect(delay.inMinutes, greaterThan(0));
        expect(delay.inMinutes, lessThan(10));
      });
    });

    group('Enhanced Auth Storage Integration', () {
      test('should store and retrieve tokens with server TTLs', () async {
        final originalTokens = AuthTokens.fromTokenResponse(
          accessToken: _createMockJwtToken(expiresInMinutes: 5),
          refreshToken: 'refresh_token',
          expiresInSeconds: 300,  // 5 minutes
          refreshExpiresInSeconds: 1800,  // 30 minutes
        );

        // Store tokens
        await storage.write(originalTokens);

        // Retrieve tokens
        final retrievedTokens = await storage.read();

        expect(retrievedTokens, isNotNull);
        expect(retrievedTokens!.access, equals(originalTokens.access));
        expect(retrievedTokens.refresh, equals(originalTokens.refresh));
        expect(retrievedTokens.accessExpiresAt, equals(originalTokens.accessExpiresAt));
        expect(retrievedTokens.refreshExpiresAt, equals(originalTokens.refreshExpiresAt));
      });

      test('should provide server TTL-aware token validation', () async {
        // Create tokens that expire soon
        final expiringTokens = AuthTokens.fromTokenResponse(
          accessToken: _createMockJwtToken(expiresInMinutes: 1),
          refreshToken: 'refresh_token',
          expiresInSeconds: 60,  // 1 minute
          refreshExpiresInSeconds: 3600,  // 1 hour
        );

        await storage.write(expiringTokens);

        // Test expiration checks
        final isExpiring = await storage.isAccessTokenExpiring();
        expect(isExpiring, isTrue);

        final isRefreshExpired = await storage.isRefreshTokenExpired();
        expect(isRefreshExpired, isFalse);
      });

      test('should migrate legacy tokens to enhanced format', () async {
        // This would test migration from existing storage format
        // Implementation depends on your existing storage structure
        await storage.migrateLegacyTokens();
        
        // Verify migration completed without errors
        final stats = await storage.getStorageStats();
        expect(stats['error'], isNull);
      });
    });

    group('Dynamic Token Scheduling with Server TTLs', () {
      testWidgets('should schedule refresh based on server TTLs', (tester) async {
        fakeAsync((async) {
          // Create tokens with known TTLs
          final tokens = AuthTokens.fromTokenResponse(
            accessToken: _createMockJwtToken(expiresInMinutes: 5),
            refreshToken: 'refresh_token',
            expiresInSeconds: 300,  // 5 minutes
            refreshExpiresInSeconds: 1800,  // 30 minutes
          );

          // Calculate expected refresh time
          final expectedRefreshTime = tokens.nextScheduledRefreshTime;
          final expectedDelay = tokens.delayUntilNextRefresh;

          expect(expectedDelay.inMinutes, greaterThan(0));
          expect(expectedDelay.inMinutes, lessThan(5));

          // Verify timing calculations
          expect(expectedRefreshTime.isAfter(DateTime.now()), isTrue);
          expect(expectedRefreshTime.isBefore(tokens.accessExpiresAt), isTrue);

          async.flushMicrotasks();
        });
      });

      test('should handle various server TTL scenarios', () {
        final testCases = [
          {'access': 120, 'refresh': 600},    // 2min access, 10min refresh
          {'access': 300, 'refresh': 1800},   // 5min access, 30min refresh
          {'access': 900, 'refresh': 3600},   // 15min access, 1hr refresh
          {'access': 60, 'refresh': 300},     // 1min access, 5min refresh (short-lived)
        ];

        for (final testCase in testCases) {
          final tokens = AuthTokens.fromTokenResponse(
            accessToken: _createMockJwtToken(expiresInMinutes: testCase['access']! ~/ 60),
            refreshToken: 'refresh_token',
            expiresInSeconds: testCase['access']!,
            refreshExpiresInSeconds: testCase['refresh']!,
          );

          // Verify refresh scheduling logic
          final nextRefresh = tokens.nextScheduledRefreshTime;
          final delay = tokens.delayUntilNextRefresh;

          expect(delay.inSeconds, greaterThan(0));
          expect(delay.inSeconds, lessThan(testCase['access']!));
          expect(nextRefresh.isBefore(tokens.accessExpiresAt), isTrue);
        }
      });
    });

    group('Enhanced Auth Controller Integration', () {
      test('should use server TTLs for auto-refresh scheduling', () async {
        // This would test the enhanced auth controller
        // Implementation depends on your dependency injection setup
        
        final tokens = AuthTokens.fromTokenResponse(
          accessToken: _createMockJwtToken(expiresInMinutes: 10),
          refreshToken: 'refresh_token',
          expiresInSeconds: 600,
          refreshExpiresInSeconds: 3600,
        );

        // Verify controller can calculate refresh timing from server TTLs
        final refreshTime = tokens.nextScheduledRefreshTime;
        final delay = tokens.delayUntilNextRefresh;

        expect(refreshTime.isAfter(DateTime.now()), isTrue);
        expect(delay.inMinutes, greaterThan(0));
        expect(delay.inMinutes, lessThan(10));
      });
    });

    group('401 Error Handling with Server TTLs', () {
      test('should never force logout on access token expiry', () {
        final tokens = AuthTokens.fromTokenResponse(
          accessToken: _createMockJwtToken(expiresInMinutes: -1), // Expired access token
          refreshToken: 'valid_refresh_token',
          expiresInSeconds: -60,  // Expired
          refreshExpiresInSeconds: 3600,  // Valid refresh token
        );

        // Access token is expired but refresh token is valid
        expect(tokens.isAccessTokenExpiring, isTrue);
        expect(tokens.isRefreshTokenExpired, isFalse);

        // Should refresh, not logout
        expect(tokens.areTokensValid, isFalse); // Overall invalid due to expired access
        // But refresh token is still valid, so no logout should occur
      });

      test('should force logout only on refresh token failure', () {
        final tokens = AuthTokens.fromTokenResponse(
          accessToken: _createMockJwtToken(expiresInMinutes: 5),
          refreshToken: 'expired_refresh_token',
          expiresInSeconds: 300,
          refreshExpiresInSeconds: -60,  // Expired refresh token
        );

        // Both tokens have issues, should force logout
        expect(tokens.isRefreshTokenExpired, isTrue);
        // This scenario should trigger logout
      });
    });

    tearDown(() async {
      await storage.clear();
      scheduler.dispose();
    });
  });
}

/// Helper function to create mock JWT tokens for testing
String _createMockJwtToken({required int expiresInMinutes}) {
  final now = DateTime.now();
  final exp = now.add(Duration(minutes: expiresInMinutes)).millisecondsSinceEpoch ~/ 1000;
  final iat = now.millisecondsSinceEpoch ~/ 1000;
  
  // This is a simplified mock - in real tests you'd use a proper JWT library
  // The JWT utility will extract exp and iat from this
  return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.'
         'eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjokeyJpYXQifSwiZXhwIjokeyJleHAifX0.'
         'signature'
         .replaceAll('{iat}', iat.toString())
         .replaceAll('{exp}', exp.toString());
}
